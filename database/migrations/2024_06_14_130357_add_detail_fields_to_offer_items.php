<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('offer_items', function (Blueprint $table) {
            $table->string('other_category_name')->nullable()->after('category_id');
            $table->string('product_variant_info')->nullable()->after('product_name');
            $table->string('product_url')->nullable()->after('product_variant_info');
            $table->text('product_description')->nullable()->after('product_url');
            $table->integer('currency_id')->unsigned()->after('price');
            $table->integer('currency_value')->unsigned()->after('currency_id');
            $table->string('document')->nullable()->after('renting_ratio');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('offer_items', function (Blueprint $table) {
            //
        });
    }
};
