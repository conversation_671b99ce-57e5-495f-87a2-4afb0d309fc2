<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customer_credibility_requests', function (Blueprint $table) {
            $table->string('email')->nullable()->after('partner_id');
            $table->string('name')->nullable()->after('email');
            $table->string('maturity')->nullable()->after('requested_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customer_credibility_requests', function (Blueprint $table) {
            $table->dropColumn('email');
            $table->dropColumn('name');
            $table->dropColumn('maturity');
        });
    }
};
