<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customer_credibility_requests', function (Blueprint $table) {
            $table->integer('partner_user_id')->nullable()->after('is_firm');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customer_credibility_requests', function (Blueprint $table) {
            $table->dropColumn('partner_user_id');
        });
    }
};
