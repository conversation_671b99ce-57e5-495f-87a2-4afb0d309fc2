<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customer_credibility_requests', function (Blueprint $table) {
            $table->decimal('approved_amount', 12, 2)->unsigned()->nullable()->change();
            $table->decimal('requested_amount', 12, 2)->unsigned()->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customer_credibility_requests', function (Blueprint $table) {
            $table->decimal('approved_amount', 10, 2)->change();
            $table->decimal('requested_amount', 10, 2)->change();
        });
    }
};
