<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customer_credibility_requests', function (Blueprint $table) {
            if (!Schema::hasColumn('customer_credibility_requests', 'ulid')) {
                $table->string('ulid', 26)->nullable()->after('contract_number')->unique();
            }
            if (!Schema::hasColumn('customer_credibility_requests', 'sms_error_message')) {
                $table->string('sms_error_message')->nullable()->after('ulid');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customer_credibility_requests', function (Blueprint $table) {
            if (Schema::hasColumn('customer_credibility_requests', 'sms_error_message')) {
                $table->dropColumn('sms_error_message');
            }
            if (Schema::hasColumn('customer_credibility_requests', 'ulid')) {
                $table->dropColumn('ulid');
            }
        });
    }
};
