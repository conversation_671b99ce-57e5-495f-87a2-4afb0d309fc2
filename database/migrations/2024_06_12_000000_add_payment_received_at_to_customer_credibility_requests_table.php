<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('customer_credibility_requests', function (Blueprint $table) {
            $table->timestamp('payment_received_at')->nullable()->after('contract_generated_at');
        });
    }

    public function down(): void
    {
        Schema::table('customer_credibility_requests', function (Blueprint $table) {
            $table->dropColumn('payment_received_at');
        });
    }
};
