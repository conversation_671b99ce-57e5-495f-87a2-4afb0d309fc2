<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('maturities', function (Blueprint $table) {
            $table->decimal('multiplier', 8, 2)->default(1.00);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::table('maturities', function (Blueprint $table) {
            $table->dropColumn('multiplier');
        });
    }
};
