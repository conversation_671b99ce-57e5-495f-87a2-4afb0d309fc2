<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('maturities', function (Blueprint $table) {
            $table->decimal('multiplier', 10, 4)->default(1.0000)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('maturities', function (Blueprint $table) {
            $table->decimal('multiplier', 8, 2)->default(1.00)->change();
        });
    }
};
