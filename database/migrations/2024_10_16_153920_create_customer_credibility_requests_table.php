<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_credibility_requests', function (Blueprint $table) {
            $table->id();
            $table->integer('partner_id')->unsigned();
            $table->string('tax_number')->nullable();
            $table->string('tckn')->nullable();
            $table->date('birth_date')->nullable();
            $table->string('phone');
            $table->string('status')->default('pending'); // pending, approved, rejected, partial approved
            $table->decimal('approved_amount', 10, 2)->unsigned()->nullable();
            $table->decimal('requested_amount', 10, 2)->unsigned()->nullable();
            $table->boolean('is_firm')->default(false);
            $table->string('note')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_credibility_requests');
    }
};
