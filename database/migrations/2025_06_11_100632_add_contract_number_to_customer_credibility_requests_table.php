<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customer_credibility_requests', function (Blueprint $table) {
            $table->string('contract_number', 8)->nullable()->unique()->after('used_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customer_credibility_requests', function (Blueprint $table) {
            $table->dropColumn('contract_number');
        });
    }
};
