<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('customer_credibility_requests', function (Blueprint $table) {
            if (!Schema::hasColumn('customer_credibility_requests', 'contract_generated_at')) {
                $table->timestamp('contract_generated_at')->nullable()->after('sms_error_message');
            }
        });
    }

    public function down(): void
    {
        Schema::table('customer_credibility_requests', function (Blueprint $table) {
            if (Schema::hasColumn('customer_credibility_requests', 'contract_generated_at')) {
                $table->dropColumn('contract_generated_at');
            }
        });
    }
};
