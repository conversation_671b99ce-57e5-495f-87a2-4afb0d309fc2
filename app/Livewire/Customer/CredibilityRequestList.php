<?php

namespace App\Livewire\Customer;

use Livewire\Component;
use Illuminate\Support\Collection;
use App\Models\CustomerCredibilityRequest;
use Livewire\Attributes\On;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;

class CredibilityRequestList extends Component
{
    use WithPagination;

    public int $perPage = 10;

    public function mount()
    {
        $this->refreshList();
    }

    #[On('contractDownloaded')]
    public function refreshList()
    {
        $this->resetPage();
    }

    public function dismissSmsError($id)
    {
        $request = CustomerCredibilityRequest::find($id);
        if ($request && $request->partner_id == Auth::user()->partners()->first()->id) {
            $request->update(['sms_error_message' => null]);
        }
        $this->refreshList();
    }

    public function openContractModal($credibilityRequestId)
    {
        $this->dispatch('openContractModal', [
            'credibilityRequestId' => $credibilityRequestId
        ]);
    }

    public function updatingPerPage()
    {
        $this->resetPage();
    }

    public function render()
    {
        $query = CustomerCredibilityRequest::where('partner_id', Auth::user()->partners()->first()->id);

        // Eğer kullanıcı firma yöneticisi değilse, sadece kendi oluşturduğu talepleri getir
        if (!Auth::user()->partners()->first()->pivot->is_company_manager) {
            $query->where('partner_user_id', Auth::user()->id);
        }

        $credibilityRequests = $query->orderBy('created_at', 'desc')
            ->paginate($this->perPage);

        // Pagination view'ını manuel olarak ayarla
        $credibilityRequests->withPath(request()->url());

        return view('livewire.customer.credibility-request-list', [
            'credibilityRequests' => $credibilityRequests
        ]);
    }
}
