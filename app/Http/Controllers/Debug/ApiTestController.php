<?php

namespace App\Http\Controllers\Debug;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ApiTestController extends Controller
{
    /**
     * API test isteğini execute et
     */
    public function executeTest(Request $request)
    {
        // Validation
        $validator = Validator::make($request->all(), [
            'method' => 'required|in:GET,POST,PUT,DELETE,PATCH',
            'url' => 'required|string',
            'headers' => 'nullable|array',
            'body' => 'nullable|string',
            'is_multipart' => 'nullable|boolean',
            'form_data' => 'nullable|array',
            'files' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 422);
        }

        $startTime = microtime(true);

        try {
            // Base URL'i hazırla
            $baseUrl = $request->input('base_url', config('app.scoring_api.base_url'));
            $fullUrl = $this->buildFullUrl($baseUrl, $request->input('url'));

            // Headers'ı hazırla
            $headers = $this->prepareHeaders($request->input('headers', []));

            // HTTP client oluştur
            $httpClient = Http::withHeaders($headers)
                ->timeout(30)
                ->retry(1, 100);

            // Body veya form data'yi hazırla
            $requestData = $this->prepareRequestData($request);

            // Request'i gönder
            $response = $this->sendRequest(
                $httpClient,
                $request->input('method'),
                $fullUrl,
                $requestData
            );

            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2); // ms cinsinden

            $result = [
                'success' => true,
                'request' => [
                    'method' => $request->input('method'),
                    'url' => $fullUrl,
                    'headers' => $headers,
                    // 'body' => $this->serializeForJson($requestData),
                ],
                'response' => [
                    'status' => $response->status(),
                    'headers' => $this->cleanResponseHeaders($response->headers()),
                    'body' => $this->parseResponseBody($response),
                    'duration' => $duration . ' ms',
                ],
                'timestamp' => now()->format('Y-m-d H:i:s'),
            ];

            // Log the test
            $this->logApiTest($result);

            return $this->safeJsonResponse($result);
        } catch (\Exception $e) {
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);

            $errorResult = [
                'success' => false,
                'error' => $e->getMessage(),
                'request' => [
                    'method' => $request->input('method'),
                    'url' => $fullUrl ?? $request->input('url'),
                    'headers' => $headers ?? [],
                    // 'body' => $this->serializeForJson($requestData ?? null),
                ],
                'response' => [
                    'status' => null,
                    'headers' => [],
                    'body' => null,
                ],
                'duration' => $duration . ' ms',
                'timestamp' => now()->format('Y-m-d H:i:s'),
            ];

            $this->logApiTest($errorResult);

            return $this->safeJsonResponse($errorResult, 500);
        }
    }

    /**
     * Full URL oluştur
     */
    private function buildFullUrl($baseUrl, $path)
    {
        $baseUrl = rtrim($baseUrl, '/');
        $path = ltrim($path, '/');

        return $baseUrl . '/' . $path;
    }

    /**
     * Headers'ı hazırla
     */
    private function prepareHeaders($headers)
    {
        $prepared = [];

        if (is_array($headers)) {
            foreach ($headers as $key => $value) {
                if (!empty($key) && !empty($value)) {
                    $prepared[$key] = $value;
                }
            }
        }

        return $prepared;
    }

    /**
     * Request data'sini hazırla (JSON body veya multipart form data)
     */
    private function prepareRequestData($request)
    {
        $isMultipart = $request->input('is_multipart', false);

        if ($isMultipart) {
            return $this->prepareMultipartData($request);
        }

        return $this->parseBody($request->input('body'));
    }

    /**
     * Multipart form data hazırla
     */
    private function prepareMultipartData($request)
    {
        $formData = [];

        // Form data alanlarını ekle
        $fields = $request->input('form_data', []);
        foreach ($fields as $field) {
            if (!empty($field['key'])) {
                $formData[$field['key']] = $field['value'] ?? '';
            }
        }

        // Dosyaları ekle
        $files = $request->input('files', []);
        foreach ($files as $fileData) {
            if (!empty($fileData['key'])) {
                if (!empty($fileData['file_path'])) {
                    // Gerçek dosya yolu verilmişse
                    $filePath = storage_path($fileData['file_path']);
                    if (file_exists($filePath)) {
                        $formData[$fileData['key']] = fopen($filePath, 'r');
                    }
                } else {
                    // Default test dosyası kullan
                    $defaultPath = storage_path('testpdf/1.pdf');
                    if (file_exists($defaultPath)) {
                        $formData[$fileData['key']] = fopen($defaultPath, 'r');
                    }
                }
            }
        }

        return $formData;
    }

    /**
     * Request body'sini parse et
     */
    private function parseBody($body)
    {
        if (empty($body)) {
            return null;
        }

        // JSON string'i array'e çevir
        $decoded = json_decode($body, true);

        if (json_last_error() === JSON_ERROR_NONE) {
            return $decoded;
        }

        // JSON değilse string olarak dön
        return $body;
    }

    /**
     * HTTP request gönder
     */
    private function sendRequest($httpClient, $method, $url, $body)
    {
        // Multipart verisi varsa attach() metodunu kullan
        if (is_array($body) && $this->hasFileResources($body)) {
            return $this->sendMultipartRequest($httpClient, $method, $url, $body);
        }

        switch (strtoupper($method)) {
            case 'GET':
                return $httpClient->get($url);
            case 'POST':
                return $httpClient->post($url, $body);
            case 'PUT':
                return $httpClient->put($url, $body);
            case 'DELETE':
                return $httpClient->delete($url, $body);
            case 'PATCH':
                return $httpClient->patch($url, $body);
            default:
                throw new \InvalidArgumentException('Unsupported HTTP method: ' . $method);
        }
    }

    /**
     * Body'de file resource'ları var mı kontrol et
     */
    private function hasFileResources($body)
    {
        if (!is_array($body)) {
            return false;
        }

        foreach ($body as $value) {
            if (is_resource($value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Multipart request gönder
     */
    private function sendMultipartRequest($httpClient, $method, $url, $body)
    {
        // Laravel Http Client multipart request builder
        $builder = $httpClient;
        $fileKeys = []; // Dosya key'lerini takip et

        // Verileri işle
        foreach ($body as $key => $value) {
            if (is_resource($value)) {
                $fileKeys[] = $key; // Dosya key'ini kaydet

                // File stream'den dosya bilgilerini al
                $meta = stream_get_meta_data($value);
                $filePath = $meta['uri'] ?? null;

                if ($filePath && file_exists($filePath)) {
                    // Dosyayı attach et
                    $builder = $builder->attach(
                        $key,
                        file_get_contents($filePath),
                        basename($filePath)
                    );
                }

                // Stream'i kapat
                if (is_resource($value)) {
                    fclose($value);
                }
            } else {
                // Normal form field - attach ile ekle
                $builder = $builder->attach($key, (string) $value);
            }
        }

        // File resource'ları body'den kaldır (kapatıldığı için JSON'a serialize edilemez)
        foreach ($fileKeys as $key) {
            $body[$key] = '[File Uploaded: ' . $key . ']';
        }

        // Request'i gönder
        switch (strtoupper($method)) {
            case 'POST':
                return $builder->post($url);
            case 'PUT':
                return $builder->put($url);
            case 'PATCH':
                return $builder->patch($url);
            default:
                throw new \InvalidArgumentException('Multipart requests only support POST, PUT, PATCH methods');
        }
    }

    /**
     * Response headers'ı temizle
     */
    private function cleanResponseHeaders($headers)
    {
        $cleaned = [];

        foreach ($headers as $key => $values) {
            if (is_array($values)) {
                // Her zaman string'e çevir - array'leri JSON'a serialize etme
                if (count($values) === 1) {
                    $cleaned[$key] = (string) $values[0];
                } else {
                    $cleaned[$key] = '[' . count($values) . ' values]';
                }
            } else {
                $cleaned[$key] = (string) $values;
            }
        }

        return $cleaned;
    }

    /**
     * Response body'sini parse et
     */
    private function parseResponseBody($response)
    {
        $body = $response->body();

        // JSON response'u parse etmeye çalış
        $json = json_decode($body, true);

        if (json_last_error() === JSON_ERROR_NONE) {
            return $json;
        }

        return $body;
    }

    /**
     * Veriyi JSON için güvenli hale getir
     */
    private function serializeForJson($data)
    {
        if ($data === null) {
            return null;
        }

        // Array ise her elemanını kontrol et
        if (is_array($data)) {
            $serialized = [];
            foreach ($data as $key => $value) {
                $serialized[$key] = $this->serializeForJson($value);
            }
            return $serialized;
        }

        // Object ise string'e çevir veya array'e dönüştür
        if (is_object($data)) {
            $className = get_class($data);

            // Laravel Response headers özel handling
            if (
                $className === 'Illuminate\Http\Client\Response' ||
                $className === 'GuzzleHttp\Psr7\Response' ||
                strpos($className, 'Response') !== false
            ) {
                return '[Response Object]';
            }

            if (method_exists($data, 'toArray')) {
                return $this->serializeForJson($data->toArray());
            }
            if (method_exists($data, '__toString')) {
                return (string) $data;
            }

            // Illuminate collections ve diğer Laravel objelerini handle et
            if (strpos($className, 'Illuminate\\') === 0) {
                if (method_exists($data, 'all')) {
                    return $this->serializeForJson($data->all());
                }
                if (method_exists($data, 'toJson')) {
                    return '[Laravel Object: ' . $className . ']';
                }
            }

            return '[Object: ' . $className . ']';
        }

        // Resource ise (file stream gibi) - closed da dahil
        if (is_resource($data)) {
            $type = get_resource_type($data);
            if ($type === 'stream') {
                $meta = stream_get_meta_data($data);
                return [
                    'type' => 'file_stream',
                    'uri' => $meta['uri'] ?? 'unknown',
                    'mode' => $meta['mode'] ?? 'unknown'
                ];
            }
            return '[Resource: ' . $type . ']';
        }

        // String'de resource pattern'ı varsa
        if (is_string($data) && (
            strpos($data, 'resource') !== false ||
            strpos($data, 'closed') !== false ||
            strpos($data, 'NULL') !== false
        )) {
            return '[Closed Resource]';
        }

        // NULL değerleri özel handle et
        if (is_null($data)) {
            return null;
        }

        // Diğer basit tipler (string, int, bool, float)
        return $data;
    }

    /**
     * Güvenli JSON response oluştur
     */
    private function safeJsonResponse($data, $status = 200)
    {
        // İlk önce veriyi JSON için temizle
        $cleanData = $this->serializeForJson($data);

        // JSON encode testi yap
        $json = json_encode($cleanData, JSON_UNESCAPED_UNICODE);
        $jsonError = json_last_error();
        $jsonErrorMsg = json_last_error_msg();

        if ($jsonError !== JSON_ERROR_NONE || $json === false) {
            // Debug bilgisini loglara yaz
            try {
                $responseDump = 'unknown';
                if (isset($cleanData['response'])) {
                    $responseDump = [
                        'type' => gettype($cleanData['response']),
                        'body_type' => isset($cleanData['response']['body']) ? gettype($cleanData['response']['body']) : 'missing',
                        'body_content' => isset($cleanData['response']['body']) ? substr(var_export($cleanData['response']['body'], true), 0, 200) : 'missing'
                    ];
                }

                Log::channel('single')->error('JSON Serialization Error', [
                    'error_code' => $jsonError,
                    'error_msg' => $jsonErrorMsg,
                    'json_result' => $json,
                    'clean_data_type' => gettype($cleanData),
                    'clean_data_keys' => is_array($cleanData) ? array_keys($cleanData) : 'not_array',
                    'response_debug' => $responseDump,
                    'basic_info' => 'Response serialization failed'
                ]);
            } catch (\Exception $logError) {
                // Log bile yazılamazsa, en azından devam et
            }

            // En minimal response döndür - hiç serialize etme
            return response([
                'success' => false,
                'error' => 'json_encode error: ' . $jsonErrorMsg . ' (Code: ' . $jsonError . ')',
                'error_details' => 'Response data could not be serialized',
                'debug_info' => 'JSON result: ' . ($json === false ? 'FALSE' : 'OTHER')
            ], 500, ['Content-Type' => 'application/json']);
        }

        return response()->json($cleanData, $status);
    }

    /**
     * Data yapısını debug et
     */
    private function debugDataStructure($data, $depth = 0)
    {
        if ($depth > 4) return '[Too Deep]';

        if (is_array($data)) {
            $debug = [];
            $count = 0;
            foreach ($data as $key => $value) {
                // Array'de çok fazla eleman varsa sadece ilk 10'unu göster
                if ($count >= 10) {
                    $debug['[...]'] = '[' . (count($data) - 10) . ' more items]';
                    break;
                }
                $debug[$key] = $this->debugDataStructure($value, $depth + 1);
                $count++;
            }
            return $debug;
        }

        if (is_object($data)) {
            return '[Object: ' . get_class($data) . ']';
        }

        if (is_resource($data)) {
            return '[Resource: ' . get_resource_type($data) . ']';
        }

        if (is_string($data)) {
            if (strpos($data, 'resource') !== false || strpos($data, 'NULL') !== false) {
                return '[Closed Resource]';
            }
            // Uzun string'leri kısalt
            if (strlen($data) > 100) {
                return '[String: ' . strlen($data) . ' chars] ' . substr($data, 0, 100) . '...';
            }
            return $data;
        }

        // Basit tipler
        if (is_null($data) || is_bool($data) || is_numeric($data)) {
            return $data;
        }

        // Diğer tipleri güvenli şekilde handle et
        try {
            $exported = var_export($data, true);
            if (strlen($exported) > 200) {
                return gettype($data) . ': [Large Data]';
            }
            return gettype($data) . ': ' . $exported;
        } catch (\Exception $e) {
            return gettype($data) . ': [Cannot Export]';
        }
    }

    /**
     * API test'ini logla
     */
    private function logApiTest($result)
    {
        // Log için safe data hazırla (dosyaları dahil etme)
        $logData = [
            'success' => $result['success'] ?? false,
            'method' => $result['request']['method'] ?? null,
            'url' => $result['request']['url'] ?? null,
            'status' => $result['response']['status'] ?? null,
            'duration' => $result['duration'] ?? null,
            'error' => $result['error'] ?? null,
            'timestamp' => $result['timestamp'] ?? null,
        ];

        // Request body'yi loglama için temizle
        if (isset($result['request']['body'])) {
            $logData['request_body_summary'] = $this->createLogSafeRequestSummary($result['request']['body']);
        }

        // Response body'yi loglama için temizle
        if (isset($result['response']['body'])) {
            $logData['response_body_size'] = is_string($result['response']['body'])
                ? strlen($result['response']['body']) . ' chars'
                : gettype($result['response']['body']);
        }

        Log::channel('single')->debug('Debug API Test Executed', $logData);
    }

    /**
     * Request body'sini log için güvenli hale getir
     */
    private function createLogSafeRequestSummary($body)
    {
        if ($body === null) {
            return null;
        }

        if (is_array($body)) {
            $summary = [];
            foreach ($body as $key => $value) {
                if (is_resource($value)) {
                    $summary[$key] = '[File Resource - ' . get_resource_type($value) . ']';
                } elseif (is_string($value) && strlen($value) > 200) {
                    $summary[$key] = '[Large String - ' . strlen($value) . ' chars]';
                } elseif (is_array($value)) {
                    $summary[$key] = '[Array with ' . count($value) . ' items]';
                } else {
                    $summary[$key] = $value;
                }
            }
            return $summary;
        }

        if (is_string($body)) {
            return strlen($body) > 200
                ? '[String - ' . strlen($body) . ' chars]'
                : $body;
        }

        return gettype($body);
    }
}
