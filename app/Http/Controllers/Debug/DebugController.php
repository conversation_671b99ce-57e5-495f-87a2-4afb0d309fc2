<?php

namespace App\Http\Controllers\Debug;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DebugController extends Controller
{
    /**
     * Debug platform dashboard
     */
    public function dashboard()
    {
        $stats = [
            'platform_status' => config('app.debug_platform_enabled'),
            'scoring_api_configured' => !empty(config('app.scoring_api.api_key')),
            'recent_tests' => $this->getRecentTestLogs(),
        ];

        return view('debug.dashboard', compact('stats'));
    }

    /**
     * API Tester sayfası
     */
    public function apiTester()
    {
        $config = [
            'scoring_api' => config('app.scoring_api'),
            'templates' => $this->getTemplates(),
        ];

        return view('debug.api-tester', compact('config'));
    }

    /**
     * Template döndür
     */
    public function getTemplate($template)
    {
        $templates = $this->getTemplates();

        if (!isset($templates[$template])) {
            return response()->json(['error' => 'Template not found'], 404);
        }

        return response()->json($templates[$template]);
    }

    /**
     * Mevcut template'ları döndür
     */
    private function getTemplates()
    {
        return [
            'scoring-api' => [
                'name' => 'Scoring API Test',
                'method' => 'POST',
                'url' => '/api/scoring',
                'headers' => [
                    'Authorization' => 'Bearer ' . config('app.scoring_api.api_key'),
                    'Content-Type' => 'application/json',
                ],
                'body' => [
                    'scoring_source_id' => config('app.scoring_api.source_id', 1),
                    'full_name' => 'Test Kullanıcı',
                    'tckn' => '12345678901',
                    'email' => '<EMAIL>',
                    'birth_date' => '1990-01-01',
                    'requested_amount' => 50000.00,
                    'requested_duration_months' => 12,
                    'additional_data' => [
                        'phone' => '+905551234567',
                        'source_data_id' => 999,
                        'renting_ratio' => 1.5,
                        'partner_data' => [
                            'name' => 'Test Partner',
                            'phone' => '+905551234568'
                        ]
                    ]
                ],
                'description' => 'SendCustomerScoringRequest job\'ı ile aynı formatta test request\'i'
            ],
            'findeks-api' => [
                'name' => 'Findeks API Test',
                'method' => 'POST',
                'url' => '/api/findex-automation/result',
                'headers' => [
                    'Authorization' => 'Bearer ',
                ],
                'is_multipart' => true,
                'form_data' => [
                    [
                        'key' => 'ulid',
                        'value' => $this->generateUlid(),
                        'description' => 'Dinamik ULID değeri'
                    ],
                    [
                        'key' => 'findexEvaluationTable',
                        'value' => $this->getFindeksHtmlContent(),
                        'description' => 'Findeks HTML rapor içeriği'
                    ]
                ],
                'files' => [
                    [
                        'key' => 'pdf_file',
                        'file_path' => 'testpdf/1.pdf',
                        'description' => 'PDF rapor dosyası'
                    ]
                ],
                'description' => 'Findeks API automation result endpoint test\'i - Multipart form data ile'
            ],
            'custom' => [
                'name' => 'Custom Request',
                'method' => 'GET',
                'url' => '',
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'body' => null,
                'description' => 'Özelleştirilebilir boş template'
            ]
        ];
    }

    /**
     * Son test loglarını getir
     */
    private function getRecentTestLogs()
    {
        // Bu implementation'ı log dosyalarından okuma yapacak
        // Şimdilik mock data dönüyoruz
        return [
            [
                'timestamp' => now()->format('Y-m-d H:i:s'),
                'method' => 'POST',
                'url' => '/api/scoring',
                'status' => 200,
                'duration' => '1.2s'
            ]
        ];
    }

    /**
     * Dinamik ULID üret
     */
    private function generateUlid()
    {
        return 'findeks-test-' . now()->format('YmdHis') . '-' . \Str::random(8);
    }

    /**
     * Findeks HTML content döndür
     */
    private function getFindeksHtmlContent()
    {
        return '<div id="raporIcerikMainPanel"><svg xmlns="http://www.w3.org/2000/svg" id="j_idt125" width="10" height="10"></svg>

	<div class="row text-center rapor-bilgileri">
		<div class="col-xs-4">
			<div class="infobox">
				<h3>Limitler Toplamı</h3>
				<div class="rapor-bilgisi">26.611 TL</div>
			</div>
		</div>
		<div class="col-xs-4">
			<div class="infobox">
				<h3>Borçlar Toplamı</h3>
				<div class="rapor-bilgisi">33.921 TL</div>
			</div>
		</div>
		<div class="col-xs-4">
			<div class="infobox">
				<h3>Kredili Ürünler</h3>
				<div class="rapor-bilgisi">5</div>
			</div>
		</div>
	</div>

	<div class="row kredi-raporu-charts">
		<div class="col-xs-6">
			<h4>Limit Kullanım Oranı</h4>
			<div class="col-xs-6">
				<div class="chart1">
					<div class="cover"></div>
					<div class="safearea">
						<div class="status"></div>
					</div>
				</div>
			</div>
			<div class="col-xs-6 limit-detay">
				<ul class="limits">
					<li>Toplam Limit</li>
					<li>26.611 TL</li>
					<li>Kullanılabilir Limit</li>
					<li>-7.310 TL</li>
					<li>Toplam Borç</li>
					<li class="borc">33.921 TL</li>
				</ul>
			</div>
		</div>

		<div class="col-xs-6">
			<h4>Kredili Ürünlerin Borç Dağılımı</h4><div class="chart2">
				<div id="krediliurunlerchart" style="height: 300px; width: 100%;" data-highcharts-chart="0"></div></div>
		</div>
	</div>

	<table width="100%" border="0" cellspacing="0" cellpadding="0">
		<tbody>
			<tr>
				<th scope="row">Çalışılan Kurum Sayısı</th>
				<td>3</td>
			</tr>
			<tr>
				<th scope="row">Son Kredi Kullanım Tarihi</th>
				<td>12/04/2025</td>
			</tr>
			<tr>
				<th scope="row">Borç / Limit Oranı</th>
				<td>%127</td>
			</tr>
			<tr>
				<th scope="row">Ödeme Tarihçesindeki En Olumsuz Durum</th>
				<td>Kanuni Takip</td>
			</tr>
			<tr>
				<th scope="row">Gecikmedeki Hesap Sayısı</th>
				<td>6</td>
			</tr>
			<tr>
				<th scope="row">Gecikmedeki Bakiye Toplamı</th>
				<td>9.402 TL</td>
			</tr>
		</tbody>
	</table>
</div>';
    }
}
