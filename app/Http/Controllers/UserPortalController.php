<?php

namespace App\Http\Controllers;

use App\Models\Offer;
use App\Models\Project;
use App\Models\User;
use App\Models\CustomerCredibilityRequest;
use App\Services\CustomerContractService;
use Illuminate\Support\Facades\Auth;
use Spatie\LaravelPdf\Enums\Format;
use Spatie\LaravelPdf\Facades\Pdf;

class UserPortalController extends Controller
{
    public function index()
    {
        // Check if the user has any partners
        if (auth()->user->partners->isEmpty()) {
            return abort(403, 'Kullanıcınız henüz hiçbir partner için yetkilendirilmemiş durumdadır.');
        }

        return view('profile/welcome');
    }

    public function offerRequests()
    {
        return view('profile/satin-alma-talepleri');
    }

    public function showOfferDetail($projectCode)
    {
        $offer = Offer::whereIn('partner_id', auth()->user->partners->pluck('id'))
            ->where('offer_code', $projectCode)
            ->firstOrFail();

        return view('profile/offer-detail')->with([
            'offer' => $offer
        ]);
    }

    public function openRequest()
    {
        return view('profile/talep-ac');
        //            ->with([
        //                'projects' => Offer::whereIn('company_id', auth()->user()->partners->pluck('id'))->get()
        //            ]);
    }

    public function paymentPlans()
    {
        return view('profile/odeme-planlarim');
    }

    public function pastRequests()
    {
        return view('profile/gecmis-taleplerim');
    }

    public function createRequest()
    {
        return view('profile/talep-olustur');
    }

    public function documents()
    {
        return view('profile/belgeler');
    }

    public function settings()
    {
        return view('profile/ayarlar');
    }

    public function downloadOffer($projectCode)
    {
        $offer = Offer::whereIn('partner_id', auth()->user->partners->pluck('id'))
            ->where('offer_code', $projectCode)
            ->firstOrFail();

        $offer = view('profile/offer-detail')->with([
            'offer' => $offer
        ])->render();

        return Pdf::html($offer)->format(Format::A4)->download($projectCode . '.pdf');
        //        return response()->download(Pdf::html($offer), $projectCode . '.pdf', [
        //            'Content-Type' => 'application/pdf',
        //            'Content-Disposition' => 'attachment'
        //        ]);
    }

    public function requestCredibility()
    {
        return view('profile/cari-kredibilite-talebi');
    }

    public function credibilityRequestList()
    {
        return view('profile/cari-kredibilite-talepleri');
    }

    public function costCalculate()
    {
        return view('profile/maliyet-hesaplama');
    }

    /**
     * Başka bir kullanıcı hesabına giriş yapma (impersonation)
     * 
     * @param string $email Giriş yapılmak istenen kullanıcının email adresi
     * @return \Illuminate\Http\RedirectResponse
     */
    public function impersonate($email)
    {
        // Geçiş yapılacak kullanıcıyı bul
        $targetUser = User::where('email', $email)->first();

        if (!$targetUser) {
            return redirect()->route('dashboard')->with('error', 'Belirtilen kullanıcı bulunamadı.');
        }

        // Mevcut kullanıcı bilgilerini sesssion'a kaydet
        session()->put('impersonator_id', auth()->user->id);

        // Hedef kullanıcı olarak giriş yap
        Auth::login($targetUser);

        return redirect()->route('dashboard')->with('success', $targetUser->name . ' kullanıcısı olarak giriş yapıldı.');
    }

    public function downloadContract(CustomerCredibilityRequest $credibilityRequest)
    {
        // Kullanıcının kendi verisi olup olmadığını kontrol et
        if (!auth()->user->partners()->whereHas('customerCredibilityRequests', function ($query) use ($credibilityRequest) {
            $query->where('id', $credibilityRequest->id);
        })->exists()) {
            abort(403, 'Bu talebe erişim yetkiniz yok.');
        }

        $contractService = new CustomerContractService();
        $filePath = $contractService->getContractPath($credibilityRequest);

        if (!file_exists($filePath)) {
            abort(404, 'Sözleşme dosyası bulunamadı.');
        }

        $filename = 'rental-request-' . $credibilityRequest->contract_number . '.pdf';

        return response()->download($filePath, $filename, [
            'Content-Type' => 'application/pdf',
        ]);
    }
}
