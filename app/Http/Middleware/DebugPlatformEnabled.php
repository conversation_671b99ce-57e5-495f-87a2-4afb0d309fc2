<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class DebugPlatformEnabled
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Debug platform enabled kontrolü
        if (!config('app.debug_platform_enabled', false)) {
            abort(404, 'Debug platform is not enabled');
        }

        return $next($request);
    }
}
