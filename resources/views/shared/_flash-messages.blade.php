
{{-- Flash Messages --}}
@if(session('message'))
    <div class="flash-message flash-success mb-4 mx-3" style="border: 2px solid #28a745; border-radius: 12px; background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2); padding: 20px; position: relative; overflow: hidden;">
        <div class="flash-icon" style="position: absolute; top: 15px; left: 20px; color: #155724; font-size: 24px;">
            <i class="fa fa-check-circle"></i>
        </div>
        <div style="margin-left: 50px; margin-right: 30px;">
            <h6 style="color: #155724; font-weight: bold; margin: 0 0 5px 0; font-size: 16px;">Başarılı!</h6>
            <p style="color: #155724; margin: 0; font-size: 14px; line-height: 1.4;">{{ session('message') }}</p>
        </div>
        <button type="button" class="flash-close" onclick="this.parentElement.style.display='none'" style="position: absolute; top: 10px; right: 15px; background: none; border: none; color: #155724; font-size: 20px; cursor: pointer; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;">
            <i class="fa fa-times"></i>
        </button>
        <div style="position: absolute; bottom: 0; left: 0; width: 100%; height: 4px; background: linear-gradient(90deg, #28a745, #20c997);"></div>
    </div>
@endif

@if(session('error'))
    <div class="flash-message flash-error mb-4 mx-3" style="border: 2px solid #dc3545; border-radius: 12px; background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2); padding: 20px; position: relative; overflow: hidden;">
        <div class="flash-icon" style="position: absolute; top: 15px; left: 20px; color: #721c24; font-size: 24px;">
            <i class="fa fa-exclamation-circle"></i>
        </div>
        <div style="margin-left: 50px; margin-right: 30px;">
            <h6 style="color: #721c24; font-weight: bold; margin: 0 0 5px 0; font-size: 16px;">Hata!</h6>
            <p style="color: #721c24; margin: 0; font-size: 14px; line-height: 1.4;">{{ session('error') }}</p>
        </div>
        <button type="button" class="flash-close" onclick="this.parentElement.style.display='none'" style="position: absolute; top: 10px; right: 15px; background: none; border: none; color: #721c24; font-size: 20px; cursor: pointer; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;">
            <i class="fa fa-times"></i>
        </button>
        <div style="position: absolute; bottom: 0; left: 0; width: 100%; height: 4px; background: linear-gradient(90deg, #dc3545, #fd7e14);"></div>
    </div>
@endif

@if(session('success'))
    <div class="flash-message flash-success mb-4 mx-3" style="border: 2px solid #28a745; border-radius: 12px; background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2); padding: 20px; position: relative; overflow: hidden;">
        <div class="flash-icon" style="position: absolute; top: 15px; left: 20px; color: #155724; font-size: 24px;">
            <i class="fa fa-check-circle"></i>
        </div>
        <div style="margin-left: 50px; margin-right: 30px;">
            <h6 style="color: #155724; font-weight: bold; margin: 0 0 5px 0; font-size: 16px;">Başarılı!</h6>
            <p style="color: #155724; margin: 0; font-size: 14px; line-height: 1.4;">{{ session('success') }}</p>
        </div>
        <button type="button" class="flash-close" onclick="this.parentElement.style.display='none'" style="position: absolute; top: 10px; right: 15px; background: none; border: none; color: #155724; font-size: 20px; cursor: pointer; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;">
            <i class="fa fa-times"></i>
        </button>
        <div style="position: absolute; bottom: 0; left: 0; width: 100%; height: 4px; background: linear-gradient(90deg, #28a745, #20c997);"></div>
    </div>
@endif

@if(session('warning'))
    <div class="flash-message flash-warning mb-4 mx-3" style="border: 2px solid #ffc107; border-radius: 12px; background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2); padding: 20px; position: relative; overflow: hidden;">
        <div class="flash-icon" style="position: absolute; top: 15px; left: 20px; color: #856404; font-size: 24px;">
            <i class="fa fa-exclamation-triangle"></i>
        </div>
        <div style="margin-left: 50px; margin-right: 30px;">
            <h6 style="color: #856404; font-weight: bold; margin: 0 0 5px 0; font-size: 16px;">Uyarı!</h6>
            <p style="color: #856404; margin: 0; font-size: 14px; line-height: 1.4;">{{ session('warning') }}</p>
        </div>
        <button type="button" class="flash-close" onclick="this.parentElement.style.display='none'" style="position: absolute; top: 10px; right: 15px; background: none; border: none; color: #856404; font-size: 20px; cursor: pointer; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;">
            <i class="fa fa-times"></i>
        </button>
        <div style="position: absolute; bottom: 0; left: 0; width: 100%; height: 4px; background: linear-gradient(90deg, #ffc107, #fd7e14);"></div>
    </div>
@endif