@extends('debug.layout')

@section('title', 'API Tester')

@section('content')
<div x-data="apiTester()" class="space-y-6">
    <!-- Header -->
    <div class="bg-gray-800 rounded-lg p-6">
        <h2 class="text-2xl font-bold text-white mb-2">API Tester</h2>
        <p class="text-gray-400">REST API'lerini test edin ve sonuçları görüntüleyin</p>
    </div>

    <!-- Template Selector -->
    <div class="bg-gray-800 rounded-lg p-6">
        <h3 class="text-lg font-medium text-white mb-4">Template Seçin</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button @click="loadTemplate('scoring-api')" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200">
                <div class="flex flex-col items-center">
                    <svg class="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Scoring API Test
                </div>
            </button>
            
            <button @click="loadTemplate('findeks-api')" 
                    class="bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200">
                <div class="flex flex-col items-center">
                    <svg class="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Findeks API Test
                </div>
            </button>
            
            <button @click="loadTemplate('custom')" 
                    class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200">
                <div class="flex flex-col items-center">
                    <svg class="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                    </svg>
                    Custom Request
                </div>
            </button>
            
            <button @click="clearForm()" 
                    class="bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200">
                <div class="flex flex-col items-center">
                    <svg class="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Temizle
                </div>
            </button>
        </div>
    </div>

    <!-- Request Configuration -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Left Panel: Request Configuration -->
        <div class="space-y-6">
            <!-- URL Configuration -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-medium text-white mb-4">Request Yapılandırması</h3>
                
                <!-- Base URL -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-400 mb-2">Base URL</label>
                    <input type="url" x-model="request.baseUrl" 
                           class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                           placeholder="https://api.example.com">
                </div>
                
                <!-- Method and Endpoint -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-400 mb-2">Method</label>
                        <select x-model="request.method" 
                                class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-blue-500">
                            <option value="GET">GET</option>
                            <option value="POST">POST</option>
                            <option value="PUT">PUT</option>
                            <option value="DELETE">DELETE</option>
                            <option value="PATCH">PATCH</option>
                        </select>
                    </div>
                    <div class="md:col-span-3">
                        <label class="block text-sm font-medium text-gray-400 mb-2">Endpoint</label>
                        <input type="text" x-model="request.endpoint" 
                               class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                               placeholder="/api/endpoint">
                    </div>
                </div>
            </div>

            <!-- Headers -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-medium text-white mb-4">Headers</h3>
                <div class="space-y-3">
                    <template x-for="(header, index) in request.headers" :key="index">
                        <div class="grid grid-cols-1 md:grid-cols-5 gap-2">
                            <div class="md:col-span-2">
                                <input type="text" x-model="header.key" 
                                       placeholder="Header Key"
                                       class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-blue-500">
                            </div>
                            <div class="md:col-span-2">
                                <input type="text" x-model="header.value" 
                                       placeholder="Header Value"
                                       class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-blue-500">
                            </div>
                            <button @click="removeHeader(index)" 
                                    class="px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition duration-200">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </template>
                    <button @click="addHeader()" 
                            class="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition duration-200">
                        + Header Ekle
                    </button>
                </div>
            </div>

            <!-- Request Type Toggle -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-medium text-white mb-4">Request Type</h3>
                <div class="flex space-x-4">
                    <label class="flex items-center cursor-pointer">
                        <input type="radio" x-model="request.isMultipart" :value="false" class="mr-2">
                        <span class="text-white">JSON Body</span>
                    </label>
                    <label class="flex items-center cursor-pointer">
                        <input type="radio" x-model="request.isMultipart" :value="true" class="mr-2">
                        <span class="text-white">Multipart Form Data</span>
                    </label>
                </div>
            </div>

            <!-- JSON Request Body -->
            <div x-show="!request.isMultipart" class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-medium text-white mb-4">Request Body (JSON)</h3>
                <div class="monaco-editor-container">
                    <textarea x-model="request.body" 
                              id="requestBodyEditor"
                              rows="15"
                              class="w-full h-96 px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 font-mono text-sm"
                              placeholder="JSON body buraya yazın..."></textarea>
                </div>
                <div class="mt-2 flex justify-between">
                    <button @click="formatJSON()" 
                            class="px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm rounded transition duration-200">
                        JSON Formatla
                    </button>
                    <button @click="validateJSON()" 
                            class="px-3 py-1 bg-yellow-600 hover:bg-yellow-700 text-white text-sm rounded transition duration-200">
                        JSON Doğrula
                    </button>
                </div>
            </div>

            <!-- Multipart Form Data -->
            <div x-show="request.isMultipart" class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-medium text-white mb-4">Form Data Fields</h3>
                <div class="space-y-3">
                    <template x-for="(field, index) in request.formData" :key="index">
                        <div class="grid grid-cols-1 md:grid-cols-5 gap-2">
                            <div class="md:col-span-2">
                                <input type="text" x-model="field.key" 
                                       placeholder="Field Key"
                                       class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-blue-500">
                            </div>
                            <div class="md:col-span-2">
                                <textarea x-model="field.value" 
                                          placeholder="Field Value"
                                          rows="2"
                                          class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"></textarea>
                            </div>
                            <button @click="removeFormField(index)" 
                                    class="px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition duration-200">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </template>
                    <button @click="addFormField()" 
                            class="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition duration-200">
                        + Form Field Ekle
                    </button>
                </div>
            </div>

            <!-- File Uploads (only for multipart) -->
            <div x-show="request.isMultipart" class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-medium text-white mb-4">File Uploads</h3>
                <div class="space-y-3">
                    <template x-for="(file, index) in request.files" :key="index">
                        <div class="grid grid-cols-1 md:grid-cols-5 gap-2">
                            <div class="md:col-span-1">
                                <input type="text" x-model="file.key" 
                                       placeholder="File Key"
                                       class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-blue-500">
                            </div>
                            <div class="md:col-span-2">
                                <input type="text" x-model="file.file_path" 
                                       placeholder="storage/testpdf/1.pdf"
                                       class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-blue-500">
                            </div>
                            <div class="md:col-span-1">
                                <span class="text-xs text-gray-400 block mt-2">Default: testpdf/1.pdf</span>
                            </div>
                            <button @click="removeFile(index)" 
                                    class="px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition duration-200">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </template>
                    <button @click="addFile()" 
                            class="w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md transition duration-200">
                        + File Upload Ekle
                    </button>
                </div>
            </div>

            <!-- Send Button -->
            <div class="flex justify-center">
                <button @click="sendRequest()" 
                        :disabled="loading"
                        :class="loading ? 'bg-gray-600 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700'"
                        class="px-8 py-3 text-white font-medium rounded-lg transition duration-200 flex items-center">
                    <svg x-show="loading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span x-text="loading ? 'Gönderiliyor...' : 'Request Gönder'"></span>
                </button>
            </div>
        </div>

        <!-- Right Panel: Response -->
        <div class="space-y-6">
            <!-- Response Status -->
            <div x-show="response.hasResponse" class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-medium text-white mb-4">Response Status</h3>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-400">Status Code</dt>
                        <dd class="mt-1 text-lg font-bold" :class="getStatusColorClass(response.status)" x-text="response.status"></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-400">Duration</dt>
                        <dd class="mt-1 text-lg font-bold text-blue-400" x-text="response.duration"></dd>
                    </div>
                </div>
                <div class="mt-4">
                    <dt class="text-sm font-medium text-gray-400">Timestamp</dt>
                    <dd class="mt-1 text-sm text-gray-300" x-text="response.timestamp"></dd>
                </div>
            </div>

            <!-- Response Headers -->
            <div x-show="response.hasResponse && response.headers" class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-medium text-white mb-4">Response Headers</h3>
                <div class="max-h-40 overflow-y-auto">
                    <template x-for="(value, key) in response.headers" :key="key">
                        <div class="grid grid-cols-2 gap-4 py-1 border-b border-gray-700 last:border-b-0">
                            <dt class="text-sm font-medium text-gray-400 truncate" x-text="key"></dt>
                            <dd class="text-sm text-gray-300 truncate" x-text="Array.isArray(value) ? value.join(', ') : value"></dd>
                        </div>
                    </template>
                </div>
            </div>

            <!-- Response Body -->
            <div x-show="response.hasResponse" class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-medium text-white mb-4">Response Body</h3>
                <div class="bg-gray-900 rounded-lg p-4 max-h-96 overflow-y-auto">
                    <pre class="text-sm text-gray-300 whitespace-pre-wrap" x-html="formatResponseBody()"></pre>
                </div>
                <div class="mt-2 flex justify-between">
                    <button @click="copyResponse()" 
                            class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition duration-200">
                        Kopyala
                    </button>
                    <span class="text-xs text-gray-400" x-text="'Size: ' + getResponseSize()"></span>
                </div>
            </div>

            <!-- Error Display -->
            <div x-show="response.hasError" class="bg-gray-800 rounded-lg p-6 border-l-4 border-red-500">
                <h3 class="text-lg font-medium text-red-400 mb-4">Error</h3>
                <p class="text-sm text-gray-300" x-text="response.error"></p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    function apiTester() {
        return {
            loading: false,
            
            request: {
                baseUrl: @json($config['scoring_api']['base_url']),
                method: 'GET',
                endpoint: '',
                headers: [
                    { key: 'Content-Type', value: 'application/json' }
                ],
                body: '',
                isMultipart: false,
                formData: [],
                files: []
            },

            response: {
                hasResponse: false,
                hasError: false,
                status: null,
                headers: null,
                body: null,
                duration: null,
                timestamp: null,
                error: null
            },

            templates: @json($config['templates']),

            init() {
                // URL'den template parametresi varsa yükle
                const urlParams = new URLSearchParams(window.location.search);
                const template = urlParams.get('template');
                if (template && this.templates[template]) {
                    this.loadTemplate(template);
                }
            },

            loadTemplate(templateName) {
                if (!this.templates[templateName]) {
                    showToast('Template bulunamadı!', 'error');
                    return;
                }

                const template = this.templates[templateName];
                
                this.request.method = template.method;
                this.request.endpoint = template.url;
                this.request.isMultipart = template.is_multipart || false;
                
                // Headers'ı yükle
                this.request.headers = [];
                Object.entries(template.headers).forEach(([key, value]) => {
                    this.request.headers.push({ key, value });
                });

                // Multipart ise form data ve files yükle
                if (template.is_multipart) {
                    this.request.formData = template.form_data ? [...template.form_data] : [];
                    this.request.files = template.files ? [...template.files] : [];
                    this.request.body = '';
                } else {
                    // JSON body'yi yükle
                    if (template.body) {
                        this.request.body = JSON.stringify(template.body, null, 2);
                    } else {
                        this.request.body = '';
                    }
                    this.request.formData = [];
                    this.request.files = [];
                }

                showToast(`${template.name} template'i yüklendi!`, 'success');
            },

            clearForm() {
                this.request = {
                    baseUrl: @json($config['scoring_api']['base_url']),
                    method: 'GET',
                    endpoint: '',
                    headers: [
                        { key: 'Content-Type', value: 'application/json' }
                    ],
                    body: '',
                    isMultipart: false,
                    formData: [],
                    files: []
                };
                this.response = {
                    hasResponse: false,
                    hasError: false,
                    status: null,
                    headers: null,
                    body: null,
                    duration: null,
                    timestamp: null,
                    error: null
                };
                showToast('Form temizlendi!', 'info');
            },

            addHeader() {
                this.request.headers.push({ key: '', value: '' });
            },

            removeHeader(index) {
                this.request.headers.splice(index, 1);
            },

            addFormField() {
                this.request.formData.push({ key: '', value: '', description: '' });
            },

            removeFormField(index) {
                this.request.formData.splice(index, 1);
            },

            addFile() {
                this.request.files.push({ key: '', file_path: '', description: '' });
            },

            removeFile(index) {
                this.request.files.splice(index, 1);
            },

            formatJSON() {
                try {
                    const parsed = JSON.parse(this.request.body);
                    this.request.body = JSON.stringify(parsed, null, 2);
                    showToast('JSON formatlandı!', 'success');
                } catch (e) {
                    showToast('Geçersiz JSON!', 'error');
                }
            },

            validateJSON() {
                try {
                    JSON.parse(this.request.body);
                    showToast('JSON geçerli!', 'success');
                } catch (e) {
                    showToast('Geçersiz JSON: ' + e.message, 'error');
                }
            },

            async sendRequest() {
                this.loading = true;
                this.response.hasResponse = false;
                this.response.hasError = false;

                try {
                    // Headers'ı obje formatına çevir
                    const headers = {};
                    this.request.headers.forEach(header => {
                        if (header.key && header.value) {
                            headers[header.key] = header.value;
                        }
                    });

                    const payload = {
                        method: this.request.method,
                        base_url: this.request.baseUrl,
                        url: this.request.endpoint,
                        headers: headers,
                        is_multipart: this.request.isMultipart,
                        body: this.request.isMultipart ? null : this.request.body,
                        form_data: this.request.isMultipart ? this.request.formData : null,
                        files: this.request.isMultipart ? this.request.files : null
                    };

                    const response = await fetch('{{ route("debug.api-test") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': window.csrf_token
                        },
                        body: JSON.stringify(payload)
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.response = {
                            hasResponse: true,
                            hasError: false,
                            status: result.response.status,
                            headers: result.response.headers,
                            body: result.response.body,
                            duration: result.response.duration,
                            timestamp: result.timestamp,
                            error: null
                        };
                        showToast('Request başarıyla gönderildi!', 'success');
                    } else {
                        this.response = {
                            hasResponse: false,
                            hasError: true,
                            error: result.error || 'Bilinmeyen hata',
                            status: null,
                            headers: null,
                            body: null,
                            duration: result.duration || null,
                            timestamp: result.timestamp || null
                        };
                        showToast('Request hatası: ' + this.response.error, 'error');
                    }
                } catch (error) {
                    this.response = {
                        hasResponse: false,
                        hasError: true,
                        error: error.message,
                        status: null,
                        headers: null,
                        body: null,
                        duration: null,
                        timestamp: null
                    };
                    showToast('Network hatası: ' + error.message, 'error');
                } finally {
                    this.loading = false;
                }
            },

            getStatusColorClass(status) {
                return window.getStatusColorClass(status);
            },

            formatResponseBody() {
                if (!this.response.body) return '';
                
                if (typeof this.response.body === 'object') {
                    return window.highlightJSON(this.response.body);
                }
                
                return this.response.body;
            },

            copyResponse() {
                const text = typeof this.response.body === 'object' 
                    ? JSON.stringify(this.response.body, null, 2)
                    : this.response.body;
                    
                navigator.clipboard.writeText(text).then(() => {
                    showToast('Response kopyalandı!', 'success');
                }).catch(() => {
                    showToast('Kopyalama hatası!', 'error');
                });
            },

            getResponseSize() {
                if (!this.response.body) return '0 B';
                
                const text = typeof this.response.body === 'object' 
                    ? JSON.stringify(this.response.body)
                    : this.response.body;
                    
                const bytes = new Blob([text]).size;
                
                if (bytes < 1024) return bytes + ' B';
                if (bytes < 1024 * 1024) return Math.round(bytes / 1024) + ' KB';
                return Math.round(bytes / (1024 * 1024)) + ' MB';
            }
        }
    }
</script>
@endpush
