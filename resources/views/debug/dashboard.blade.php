@extends('debug.layout')

@section('title', 'Debug Dashboard')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-gray-800 rounded-lg p-6">
        <h2 class="text-2xl font-bold text-white mb-2">Debug Platform Dashboard</h2>
        <p class="text-gray-400">Platform durumu ve hızlı erişim menü<PERSON>ü</p>
    </div>

    <!-- Platform Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Platform Status -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    @if($stats['platform_status'])
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    @else
                        <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                    @endif
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-medium text-white">Platform Status</h3>
                    <p class="text-sm {{ $stats['platform_status'] ? 'text-green-400' : 'text-red-400' }}">
                        {{ $stats['platform_status'] ? 'Aktif' : 'Deaktif' }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Scoring API Status -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    @if($stats['scoring_api_configured'])
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    @else
                        <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    @endif
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-medium text-white">Scoring API</h3>
                    <p class="text-sm {{ $stats['scoring_api_configured'] ? 'text-blue-400' : 'text-yellow-400' }}">
                        {{ $stats['scoring_api_configured'] ? 'Yapılandırıldı' : 'Yapılandırılmadı' }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Test Count -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-medium text-white">Son Test Sayısı</h3>
                    <p class="text-sm text-purple-400">{{ count($stats['recent_tests']) }} test yapıldı</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-gray-800 rounded-lg p-6">
        <h3 class="text-lg font-medium text-white mb-4">Hızlı İşlemler</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <a href="{{ route('debug.api-tester') }}" 
               class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 text-center">
                <div class="flex flex-col items-center">
                    <svg class="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                    </svg>
                    API Test Et
                </div>
            </a>
            
            <a href="{{ route('debug.api-tester') }}?template=scoring-api" 
               class="bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 text-center">
                <div class="flex flex-col items-center">
                    <svg class="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Scoring Test
                </div>
            </a>
            
            <a href="{{ route('debug.api-tester') }}?template=findeks-api" 
               class="bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200 text-center">
                <div class="flex flex-col items-center">
                    <svg class="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Findeks Test
                </div>
            </a>
            
            <button onclick="clearLogs()" 
                    class="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200">
                <div class="flex flex-col items-center">
                    <svg class="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Logları Temizle
                </div>
            </button>
            
            <button onclick="exportLogs()" 
                    class="bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200">
                <div class="flex flex-col items-center">
                    <svg class="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Log Dışa Aktar
                </div>
            </button>
        </div>
    </div>

    <!-- Recent Tests -->
    @if(!empty($stats['recent_tests']))
    <div class="bg-gray-800 rounded-lg p-6">
        <h3 class="text-lg font-medium text-white mb-4">Son Test Sonuçları</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-700">
                <thead>
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Zaman</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Method</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">URL</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Süre</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-700">
                    @foreach($stats['recent_tests'] as $test)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{{ $test['timestamp'] }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-900 text-blue-200">
                                {{ $test['method'] }}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-300 max-w-xs truncate">{{ $test['url'] }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="text-sm font-medium {{ $test['status'] == 200 ? 'text-green-400' : 'text-red-400' }}">
                                {{ $test['status'] }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{{ $test['duration'] }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
    @endif

    <!-- Environment Info -->
    <div class="bg-gray-800 rounded-lg p-6">
        <h3 class="text-lg font-medium text-white mb-4">Environment Bilgileri</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <dt class="text-sm font-medium text-gray-400">App Environment</dt>
                <dd class="mt-1 text-sm text-gray-300">{{ config('app.env') }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-400">App Debug</dt>
                <dd class="mt-1 text-sm text-gray-300">{{ config('app.debug') ? 'Enabled' : 'Disabled' }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-400">Scoring API Base URL</dt>
                <dd class="mt-1 text-sm text-gray-300">{{ config('app.scoring_api.base_url') }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-400">Debug Platform</dt>
                <dd class="mt-1 text-sm text-gray-300">{{ config('app.debug_platform_enabled') ? 'Enabled' : 'Disabled' }}</dd>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    function clearLogs() {
        if (confirm('Tüm debug loglarını temizlemek istediğinizden emin misiniz?')) {
            showToast('Log temizleme özelliği yakında eklenecek!', 'info');
        }
    }
    
    function exportLogs() {
        showToast('Log dışa aktarma özelliği yakında eklenecek!', 'info');
    }
</script>
@endpush
