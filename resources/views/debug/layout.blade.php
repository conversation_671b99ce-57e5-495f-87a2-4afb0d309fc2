<!DOCTYPE html>
<html lang="tr" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Debug Platform') - Partners KB</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js CDN -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Monaco Editor CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.44.0/min/vs/loader.min.js"></script>
    
    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    
    <style>
        .monaco-editor-container {
            height: 400px;
            border: 1px solid #374151;
            border-radius: 0.5rem;
        }
        
        .status-200 { @apply text-green-400; }
        .status-300 { @apply text-blue-400; }
        .status-400 { @apply text-yellow-400; }
        .status-500 { @apply text-red-400; }
        
        .json-key { @apply text-blue-300; }
        .json-string { @apply text-green-300; }
        .json-number { @apply text-yellow-300; }
        .json-boolean { @apply text-purple-300; }
        .json-null { @apply text-gray-400; }
    </style>
</head>
<body class="bg-gray-900 text-gray-100 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-800 border-b border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-bold text-white">🐛 Debug Platform</h1>
                    </div>
                    <div class="ml-6 flex space-x-8">
                        <a href="{{ route('debug.dashboard') }}" 
                           class="@if(request()->routeIs('debug.dashboard')) text-white border-b-2 border-primary-500 @else text-gray-300 hover:text-white @endif px-3 py-2 text-sm font-medium">
                            Dashboard
                        </a>
                        <a href="{{ route('debug.api-tester') }}" 
                           class="@if(request()->routeIs('debug.api-tester')) text-white border-b-2 border-primary-500 @else text-gray-300 hover:text-white @endif px-3 py-2 text-sm font-medium">
                            API Tester
                        </a>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-400">{{ config('app.name') }}</span>
                    <div class="h-6 w-6 bg-green-500 rounded-full" title="Platform Active"></div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        @yield('content')
    </main>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Global JavaScript -->
    <script>
        // CSRF Token Setup
        window.csrf_token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        // Toast Notification Function
        window.showToast = function(message, type = 'info') {
            const toast = document.createElement('div');
            const bgColor = {
                'success': 'bg-green-600',
                'error': 'bg-red-600',
                'warning': 'bg-yellow-600',
                'info': 'bg-blue-600'
            }[type] || 'bg-blue-600';
            
            toast.className = `${bgColor} text-white px-6 py-4 rounded-lg shadow-lg transform transition-all duration-300 ease-in-out translate-x-full opacity-0`;
            toast.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            `;
            
            document.getElementById('toast-container').appendChild(toast);
            
            // Animate in
            setTimeout(() => {
                toast.classList.remove('translate-x-full', 'opacity-0');
            }, 100);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                toast.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => toast.remove(), 300);
            }, 5000);
        };

        // JSON Syntax Highlighting
        window.highlightJSON = function(json) {
            if (typeof json !== 'string') {
                json = JSON.stringify(json, null, 2);
            }
            
            return json.replace(/(".*?")(\s*:\s*)?/g, function(match, key, colon) {
                if (colon) {
                    return '<span class="json-key">' + key + '</span>' + colon;
                }
                return '<span class="json-string">' + key + '</span>';
            }).replace(/:\s*(-?\d+\.?\d*)/g, ': <span class="json-number">$1</span>')
              .replace(/:\s*(true|false)/g, ': <span class="json-boolean">$1</span>')
              .replace(/:\s*(null)/g, ': <span class="json-null">$1</span>');
        };

        // Status Code Color Class
        window.getStatusColorClass = function(status) {
            if (status >= 200 && status < 300) return 'status-200';
            if (status >= 300 && status < 400) return 'status-300';
            if (status >= 400 && status < 500) return 'status-400';
            if (status >= 500) return 'status-500';
            return 'text-gray-400';
        };
    </script>
    
    @stack('scripts')
</body>
</html>
