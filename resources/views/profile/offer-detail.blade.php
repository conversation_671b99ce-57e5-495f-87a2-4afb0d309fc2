@extends('layouts.app')
@section('content')
    <div id="wrapperParallax" class="wrapper clearfix">
        <section id="blog" class="blog blog-grid pb-90 pt-30" style="background: #dfe3e8; min-height: 100vh;">
            <div class="" style="padding-left: 5%;padding-right: 5%;">
                <div class="row">
                    @include('profile.right-menu')
                    <div class="col-sm-12 col-md-12 col-lg-12 pl-400 sm-pl-15 offer-detail">
                        <div class="d-flex justify-content-between px-50 sm-px-10 py-2 mb-2 align-items-center bg-white rounded">
                            <h5 class="text-center mb-0 py-3 pdf-title">Teklifler</h5>
                            <img class="logo logo-dark mx-auto pdf-logo" style="max-width: 200px; width: 100%" src="/doc/logo/logo-large.png" alt="Kiralabunu Logo">
                            <a class="btn btn-warning pdf-button" href="{{ route('download-offer', ['projectCode' => $offer->offer_code]) }}">PDF Olarak İndir</a>
                        </div>
                        <div class="card">
                            <div class="card-header">
                                Başvuru Tarihi
                                <strong>{{ $offer->created_at->format('d/m/y H:i') }}</strong>
                                <span class="float-right"> <strong>Durum:</strong> {{ $offer->status->getLabel() }}</span>
                            </div>
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-sm-6">
                                        <h6 class="mb-3">Şirket Bilgileri:</h6>
                                        <div>
                                            <strong>Şirket Ünvanı : </strong>
                                            {{ $offer->customer->name }}
                                        </div>
                                        <div>
                                            <b>VKN : </b>
                                            {{ $offer->customer->tax_number }}
                                        </div>
                                        <div>
                                            <b>Vergi Dairesi : </b>
                                            {{ $offer->customer->tax_office }}
                                        </div>
                                        <div>
                                            <b>Telefon : </b>
                                            {{ $offer->customer->phone }}
                                        </div>
                                        <div>
                                            <b>Adres : </b>
                                            {{ $offer->customer->address }}
                                        </div>
                                    </div>

                                    <div class="col-sm-6">
                                        <h6 class="mb-3">Teklif bilgileri:</h6>
                                        <div>
                                            <b>Proje Numarası</b>
                                            {{ $offer->offer_code }}
                                        </div>
                                        <div>
                                            <b>İstenilen Kira Süresi</b>
                                            {{ $offer->maturity->name }}
                                        </div>
                                        <div>
                                            <b>Proje Bedeli</b>
                                            {{ number_format($offer->offerItems->sum('price')  , 2, ',', '.') }} {{ $offer->currency->code }}
                                        </div>
                                        <div>
                                            <b>Aylık Kira Bedeli</b>
                                            {{ number_format($offer->offerItems->sum('renting_total_price_with_taxes'), 2)}} {{ $offer->currency->code }}
                                        </div>
                                        <div>
                                            <b>Başvuru Zamanı</b>
                                            {{ $offer->created_at->format('d/m/y H:i') }}
                                        </div>
                                    </div>
                                </div>
                                <div class="table-responsive-sm">
                                    <table class="table table-striped">
                                        <thead>
                                        <tr>
                                            <th>Ürün Adı</th>
                                            <th>Kira Süresi</th>
                                            <th class="center">Adet</th>
                                            <th class="text-right">KDV Hariç Aylık Kira <br> + Hizmet Bedeli <br>(Adet)</th>
                                            {{--                                            <th class="text-right">KDV Dahil Aylık Kira <br> + Hizmet Bedeli <br>(Adet)</th>--}}
                                            <th class="text-right">KDV Hariç Aylık Kira <br> + Hizmet Bedeli <br>(Toplam)</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach($offer->offerItems as $key => $item)
                                            <tr>
                                                <td class="center">{{ $item->product_name }}</td>
                                                <td class="left strong">{{ $item->maturity->name }}</td>
                                                <td class="left">{{ $item->quantity  }}</td>
                                                <td class="text-right">{{ number_format($item->renting_unit_price, 2, ',', '.') }} {{ $offer->currency->code }}</td>
                                                {{--                                                <td class="text-right">{{ number_format($item->renting_unit_price_with_taxes, 2, ',', '.') }} {{ $offer->currency->code }}</td>--}}
                                                <td class="text-right">{{ number_format($item->renting_total_price, 2, ',', '.') }} {{ $offer->currency->code }}</td>
                                            </tr>
                                        @endforeach
                                        {{--                                        <tr>--}}
                                        {{--                                            <td class="center">4</td>--}}
                                        {{--                                            <td class="left">Platinum Support</td>--}}
                                        {{--                                            <td class="left">1 year subcription 24/7</td>--}}
                                        {{--                                            <td class="center">1</td>--}}
                                        {{--                                            <td class="text-right">$3.999,00</td>--}}
                                        {{--                                            <td class="text-right">$3.999,00</td>--}}
                                        {{--                                        </tr>--}}
                                        </tbody>
                                    </table>
                                </div>
                                <div class="row">
                                    <div class="col-lg-4 col-sm-5"></div>
                                    <div class="col-lg-4 col-sm-5 ml-auto">
                                        <table class="table table-clear">
                                            <tbody>
                                            <tr>
                                                <td class="left">
                                                    <strong>Toplam</strong>
                                                </td>
                                                <td class="text-right">{{ number_format($offer->total_price, 2, ',', '.') }} {{ $offer->currency->code }}</td>
                                            </tr>
                                            <tr>
                                                <td class="left">
                                                    <strong>KDV (20%)</strong>
                                                </td>
                                                <td class="text-right">{{ number_format($offer->total_price * .2, 2, ',', '.') }} {{ $offer->currency->code }}</td>
                                            </tr>
                                            <tr>
                                                <td class="left">
                                                    <strong>Aylık Kira</strong>
                                                </td>
                                                <td class="text-right">
                                                    <strong>{{ number_format($offer->total_price * 1.2, 2, ',', '.') }} {{ $offer->currency->code }}</strong>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>

                                    </div>

                                </div>

                            </div>
                        </div>
                    </div>
                </div><!-- .col-lg-4 end -->
            </div>
    </div>
    <!-- .container end -->
    </section>
    <div id="back-to-top" class="backtop"><i class="fa fa-long-arrow-up"></i></div>
    </div>
@endsection
