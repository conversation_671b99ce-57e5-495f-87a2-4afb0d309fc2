@extends('layouts.app')
@section('content')
    <div id="wrapperParallax" class="wrapper clearfix">
        <section id="blog" class="blog blog-grid pb-90 pt-30" style="background: #dfe3e8; min-height: 100vh;">
            <div class="" style="padding-left: 5%;padding-right: 5%;">
                <div class="row">
                    @include('profile.right-menu')
                    <div class="col-sm-12 col-md-12 col-lg-12 pl-400 sm-pl-15">
                        <div class="d-flex justify-content-between px-5 py-2 mb-2 align-items-center bg-white rounded">
                            <h5 class="text-center mb-0 py-3"><PERSON><PERSON><PERSON></h5>
                        </div>
                        <div class="single-product bg-white rounded">
                            <livewire:documents.documents/>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection

@section('popup')
    <div class="modal micromodal-slide" id="modal-1" aria-hidden="false" style="position:relative;z-index:3333;">
        <div class="modal__overlay" tabindex="-1" data-micromodal-close>
            <div class="modal__container" role="dialog" aria-modal="true" aria-labelledby="modal-1-title" style="max-width: 800px;">
                <header class="modal__header" style="height: 20px;">
                    <h2 class="modal__title" id="modal-1-title"></h2>
                    <button class="modal__close" aria-label="Close modal" data-micromodal-close></button>
                </header>
                <main class="modal__content" id="modal-1-content">
                    <div class="contact pt-2 px-0 pb-5">
                        <h5 class="text-center mb-0 py-3">Döküman Ekle</h5>
                        <div class="contact--panel">
                            <div class="contact--body">
                                <livewire:documents.upload/>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/micromodal/dist/micromodal.min.js?v=43543"></script>
    <script>
        function addDocumentPopup() {
            MicroModal.show('modal-1',
                {
                    onShow: modal => console.info(`${modal.id} is shown`), // [1]
                    // onClose: modal => $('#modal-1-content').html(''), // [2]
                    openClass: 'is-open', // [5]
                    disableScroll: true, // [6]
                    disableFocus: false, // [7]
                    awaitOpenAnimation: false, // [8]
                    awaitCloseAnimation: false, // [9]
                    debugMode: true // [10]
                }
            );
        }
    </script>
    <link rel="stylesheet" href="/assets/css/modal.css?v=002">
@endsection
