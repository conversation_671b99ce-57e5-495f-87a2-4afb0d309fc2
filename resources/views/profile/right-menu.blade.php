<div class="col-sm-12 col-md-12 col-lg-12 side-menu"
    x-data="{ open: true, get isMobile() { return window.innerWidth <= 768 ? true : false}, toggle() { this.open = ! this.open } }"
    x-on:resize.window="isMobile = window.innerWidth <= 768 ? true : false;">
    <div id="profile-menu" class="module module-sidearea w-100 h-100" x-bind:class="(open && !isMobile) || !isMobile ? 'module-active': ''">
        <div class="module-icon sidearea-icon" @click="toggle()">
            <i class="kolaso-Side"></i>
        </div>
        <div class="module-content module-sidearea-wrap">
            <div class="module-sidearea-inner">
                <div class="sidearea-header">
                    <a class="logo px-3" href="{{ route('dashboard') }}">
                        <img class="logo logo-dark mx-auto" style="max-width: 200px; width: 100%" src="/doc/logo/light-logo.png" alt="Kiralabunu Logo">
                    </a>
                </div>
                <div class=" widget widget-categories">
                    <div class="widget--content">
                        <ul class="list-unstyled">
                            <li><a wire:navigate.hover href="{{ route('dashboard') }}"> Ana Sayfa </a></li>
                            <li>
                                <button class="btn btn-toggle d-inline-flex align-items-center rounded border-0" data-bs-toggle="collapse" data-bs-target="#home-collapse" aria-expanded="true">
                                    Teklifler
                                </button>
                                <div class="collapse show" id="home-collapse">
                                    <ul class="btn-toggle-nav list-unstyled fw-normal pb-1 small">
                                        <li><a wire:navigate.hover href="{{ route('offer.index') }}" class="link-body-emphasis d-inline-flex text-decoration-none rounded">Teklif Listesi</a></li>
                                        <li><a wire:navigate.hover href="{{ route('offer.create') }}" class="link-body-emphasis d-inline-flex text-decoration-none rounded">Yeni Teklif Oluştur</a></li>
                                        <li><a wire:navigate.hover href="{{ route('cost.calculate') }}" class="link-body-emphasis d-inline-flex text-decoration-none rounded">Maliyet Hesaplama</a></li>
                                    </ul>
                                </div>
                            </li>

                            <li>
                                <button class="btn btn-toggle d-inline-flex align-items-center rounded border-0" data-bs-toggle="collapse" data-bs-target="#home-collapse" aria-expanded="true">
                                    Findeks Sorgusu Talepleri
                                </button>
                                <div class="collapse show" id="home-collapse">
                                    <ul class="btn-toggle-nav list-unstyled fw-normal pb-1 small">
                                        <li><a wire:navigate.hover href="{{ route('customer.credibility_request_list') }}" class="link-body-emphasis d-inline-flex text-decoration-none rounded">Findeks Sorgusu Talepleri</a></li>
                                        <li><a wire:navigate.hover href="{{ route('customer.request_credibility') }}" class="link-body-emphasis d-inline-flex text-decoration-none rounded">Findeks Sorgusu Talebi Oluştur</a></li>
                                    </ul>
                                </div>
                            </li>

                            {{-- <li><a wire:navigate.hover href="{{ route('documents') }}">Belgeler</a></li>--}}
                            <li><a wire:navigate.hover href="{{ route('settings') }}">Ayarlar</a></li>
                            <li><a href="{{ route('logged-out') }}">Çıkış Yap</a></li>
                        </ul>
                    </div>
                </div> <!-- .widget-categories end -->
            </div>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // new ResizeObserver((entries) => {
    //     console.log(entries[0].contentRect.width);
    //     console.log("ResizeObserver fired");
    //     // Ekran genişliğini takip için observer gerekiyor
    //     if (screen.width > '768') {
    //         var menu = document.getElementById("profile-menu");
    //         // check menu has not class module-active then add class
    //         if (!menu.classList.contains("module-active"))
    //             menu.classList.add("module-active");
    //     }
    // }).observe(document.body)
</script>