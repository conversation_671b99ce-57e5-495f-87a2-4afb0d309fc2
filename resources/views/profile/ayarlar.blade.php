@extends('layouts.app')
@section('content')
<div id="wrapperParallax" class="wrapper clearfix">
    <section id="blog" class="blog blog-grid pb-90 pt-30" style="background: #dfe3e8; min-height: 100vh;">
        <div class="" style="padding-left: 5%;padding-right: 5%;">
            <div class="row">
                @include('profile.right-menu')
                <div class="col-sm-12 col-md-12 col-lg-12 pl-400 sm-pl-15">
                    <div class="d-flex justify-content-between px-5 py-2 mb-2 align-items-center bg-white rounded">
                        <h5 class="text-center mb-0 py-3">Ayarlar</h5>
                    </div>
                    <div class="single-product bg-white rounded">
                        <livewire:settings.settings />
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div id="back-to-top" class="backtop"><i class="fa fa-long-arrow-up"></i></div>
</div>
@endsection

@section('popup')
<script src="https://unpkg.com/micromodal/dist/micromodal.min.js?v=435433"></script>
<script>
    function addUserPopup() {
        MicroModal.show('modal-1', {
            onShow: modal => console.info(`${modal.id} is shown`), // [1]
            // onClose: modal => $('#modal-1-content').html(''), // [2]
            openClass: 'is-open', // [5]
            disableScroll: true, // [6]
            disableFocus: false, // [7]
            awaitOpenAnimation: false, // [8]
            awaitCloseAnimation: false, // [9]
            debugMode: true // [10]
        });
    }
</script>
<script>
    function editUserPopup() {
        MicroModal.show('modal-2', {
            onShow: modal => console.info(`${modal.id} is shown`), // [1]
            // onClose: modal => $('#modal-1-content').html(''), // [2]
            openClass: 'is-open', // [5]
            disableScroll: true, // [6]
            disableFocus: false, // [7]
            awaitOpenAnimation: false, // [8]
            awaitCloseAnimation: false, // [9]
            debugMode: true // [10]
        });
    }
</script>
<script>
    function editCompany() {
        MicroModal.show('modal-4', {
            onShow: modal => console.info(`${modal.id} burasır is shown`), // [1]
            // onClose: modal => $('#modal-1-content').html(''), // [2]
            openClass: 'is-open', // [5]
            disableScroll: true, // [6]
            disableFocus: false, // [7]
            awaitOpenAnimation: false, // [8]
            awaitCloseAnimation: false, // [9]
            debugMode: true // [10]
        });
    }

    function editUser(userId) {
        Livewire.dispatch('setEditUser', {
            userId: userId
        });
        MicroModal.show('modal-5', {
            onShow: modal => console.info(`${modal.id} açıldı`),
            openClass: 'is-open',
            disableScroll: true,
            disableFocus: false,
            awaitOpenAnimation: false,
            awaitCloseAnimation: false,
            debugMode: true
        });
    }

    Livewire.on('closeModal', () => {
        MicroModal.close('modal-5');
    });
</script>
<link rel="stylesheet" href="/assets/css/modal.css?v=003">
@endsection