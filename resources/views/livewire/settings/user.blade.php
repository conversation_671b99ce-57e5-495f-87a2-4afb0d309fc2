<div role="tabpanel" class="tab-pane" id="details">
    <div class="d-flex justify-content-between sm-justify-content-center flex-wrap px-0 py-2 mb-2 align-items-center bg-white rounded">
        <h6 class="text-center mb-0 py-3">Birden Fazla Kullanıcı ekleyebilirsiniz</h6>
        <a class="btn btn--primary" style="color: #ffffff;" onclick="addUserPopup()">Kullan<PERSON><PERSON><PERSON> Ekle</a>
    </div>
    <div class="single-product pt-2 px-0 pb-5">
        <ul class="product-review list-unstyled bg-white rounded">
            @foreach($users as $user)
                <li class="review-comment">
                    <div class="row justify-content-center users-detail">
                        <div class="col-md-2">
                            <p class="text-black font-11 font-weight-bold">Ad Soyad</p>
                            <p class="text-black font-15 ">{{ $user->name }}</p>
                        </div>
                        <div class="col-md-3 col-lg-3">
                            <p class="text-black font-11 font-weight-bold">Email</p>
                            <p class="text-black font-15 ">{{ $user->email }}</p>
                        </div>
                        <div class="col-md-2">
                            <p class="text-black font-11 font-weight-bold">Telefon</p>
                            <p class="text-black font-15 ">{{ $user->phone }}</p>
                        </div>
                        <div class="col-md-2">
                            <p class="text-black font-11 font-weight-bold">TCKN</p>
                            <p class="text-black font-15 ">{{ str($user->tckn)->mask('*', 2, 7) }}</p>
                        </div>

                        <div class="col-md-1 d-flex align-items-center">
                            <a class="btn" style="line-height:18px; width: 100%; height: auto;" disabled="">
                                {{--                                <img style="width: 40%;" src="/doc/edit-icon.png">--}}
                                pasif et
                            </a>
                        </div>
                    </div>
                </li>
            @endforeach
        </ul>
    </div>

    <div class="modal micromodal-slide" id="modal-1" aria-hidden="false" style="position:relative;z-index:3333;">
        <div class="modal__overlay" tabindex="-1" data-micromodal-close>
            <div class="modal__container" role="dialog" aria-modal="true" aria-labelledby="modal-1-title" style="max-width: 600px;">
                <header class="modal__header" style="height: 20px;">
                    <h2 class="modal__title" id="modal-1-title"></h2>
                    <button class="modal__close" aria-label="Close modal" data-micromodal-close></button>
                </header>
                <main class="modal__content" id="modal-1-content">
                    <livewire:settings.new-user/>
                </main>
            </div>
        </div>
    </div>
    <div class="modal micromodal-slide" id="modal-2" aria-hidden="false" style="position:relative;z-index:3333;">
        <div class="modal__overlay" tabindex="-1" data-micromodal-close>
            <div class="modal__container" role="dialog" aria-modal="true" aria-labelledby="modal-1-title" style="max-width: 600px;">
                <header class="modal__header" style="height: 20px;">
                    <h2 class="modal__title" id="modal-1-title"></h2>
                    <button class="modal__close" aria-label="Close modal" data-micromodal-close></button>
                </header>
                <main class="modal__content" id="modal-1-content">
                    <livewire:settings.edit-user/>
                </main>
            </div>
        </div>
    </div>

</div>
