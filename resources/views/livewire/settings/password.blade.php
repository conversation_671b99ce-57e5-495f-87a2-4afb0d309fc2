<div role="tabpanel" class="tab-pane" id="password">
    <div class="contact pt-2 px-0 pb-5">
        @if($errors->all())
            <div class="alert alert-danger">
                <ul style="margin-bottom: 0">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif
        <div class="contact--panel" >
            <div class="contact--body">
                <form wire:submit="save" class="mb-0">
                    <div class="row">
                        <div class="col-sm-12 col-md-12 col-lg-12">
                            <input wire:model="password" type="password" name="password" class="form-control" placeholder="*Şifre" required="" aria-required="true">
                        </div>
                        <div class="col-sm-12 col-md-12 col-lg-12">
                            <input wire:model="password_confirmation" type="password" name="password_confirm" class="form-control" placeholder="*<PERSON><PERSON>re Tekrar" required="" aria-required="true">
                        </div>
                        <ul class="pl-4">
                            <li class=""><i class="fa fa-check"></i>En az 1 büyük karakter olmalıdır</li>
                            <li><i class="fa fa-check"></i>En az 1 küçük karakter olmadılır</li>
                            <li><i class="fa fa-check"></i>En az 1 özel karakter olmalıdır</li>
                            <li><i class="fa fa-check"></i>En az 8 haneden oluşmalıdır.</li>
                        </ul>
                        <div class="col-sm-12 col-md-12 col-lg-12 row justify-content-center">
                            <input type="submit" value="Güncelle" class="btn btn-warning">
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
