<div role="tabpanel" class="tab-pane active show" id="description">
    <div class="single-product pt-2 px-0 pb-5">
        <ul class="product-review list-unstyled bg-white rounded">
            <li class="review-comment">
                <h6 class="text-center mb-0"> Partner Bilgileri</h6>
            </li>
            @foreach($firms as $firm)
            <li class="review-comment">
                <div class="row justify-content-center users-detail">
                    <div class="col-md-2">
                        <p class="text-black font-11 font-weight-bold">Partner Ünvanı</p>
                        <p class="text-black font-15 ">{{ $firm->name }}</p>
                    </div>
                    <div class="col-md-2">
                        <p class="text-black font-11 font-weight-bold">VKN</p>
                        <p class="text-black font-15 ">{{ $firm->tax_number }} </p>
                    </div>
                    <div class="col-md-2">
                        <p class="text-black font-11 font-weight-bold">Vergi Dairesi</p>
                        <p class="text-black font-15">{{ $firm->tax_office }} </p>
                    </div>
                    <div class="col-md-3">
                        <p class="text-black font-11 font-weight-bold">Adres</p>
                        <p class="text-black font-15 ">{{ $firm->address }} </p>
                    </div>
                    <div class="col-md-2">
                        <p class="text-black font-11 font-weight-bold">Partner Tel</p>
                        <p class="text-black font-15 ">{{ $firm->phone }}</p>
                    </div>
                </div>
            </li>
            @endforeach
        </ul>
    </div>

    <div class="single-product pt-2 px-0 pb-5">
        <ul class="product-review list-unstyled bg-white pt-5 rounded">
            <li class="review-comment" style="padding: 30px;">
                <div class=" mb-3">
                    <h6 class="text-center"> Kullanıcı Bilgileri</h6>
                </div>
                <div class=" border-bottom mb-3">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="float-left pr-3">Ad Soyad : </h6>
                            <p class="text-black font-18">{{ auth()->user()->name }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="float-left pr-3">Email : </h6>
                            <p class="text-black">{{ auth()->user()->email }}</p>
                        </div>
                    </div>
                </div>
                <div class=" border-bottom mb-3">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="float-left pr-3">Telefon</h6>
                            <p class="text-black">{{ auth()->user()->phone }}</p>
                        </div>

                        <div class="col-md-6">
                            <h6 class="float-left pr-3">TCKN</h6>
                            <p class="text-black">{{ auth()->user()->tckn }}</p>
                        </div>
                    </div>
                </div>
            </li><!-- .review-comment end -->

        </ul>
    </div>

    @if(auth()->user()->partners->first()->pivot->is_company_manager)
    <div class="single-product pt-2 px-0 pb-5">
        <ul class="product-review list-unstyled bg-white pt-5 rounded">
            <li class="review-comment" style="padding: 30px;">
                <div class="mb-5 position-relative">
                    <div class="text-center">
                        <h6 class="mb-0">Partner Kullanıcıları</h6>
                    </div>
                    <!-- onclick="MicroModal.show('modal-4')" -->
                    <button class="btn btn-primary btn-sm position-absolute"

                        onclick="editCompany()"
                        style="top: -20px; right: 0;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-plus mr-1" viewBox="0 0 16 16">
                            <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z" />
                        </svg>
                        Yeni Kullanıcı Ekle
                    </button>
                </div>
                @if(auth()->user()->partners->first())
                @foreach(auth()->user()->partners->first()->users as $user)
                <div class="border-bottom mb-3">
                    <div class="row">
                        <div class="col-md-4">
                            <h6 class="float-left pr-3">Ad Soyad:</h6>
                            <p class="text-black">{{ $user->name }}</p>
                        </div>
                        <div class="col-md-4">
                            <h6 class="float-left pr-3">Email:</h6>
                            <p class="text-black">{{ $user->email }}</p>
                        </div>
                        <div class="col-md-3">
                            <h6 class="float-left pr-3">Firma Yöneticisi:</h6>
                            <p class="text-black">{{ $user->pivot->is_company_manager ? 'Evet' : 'Hayır' }}</p>
                        </div>
                        <div class="col-md-1 text-right">
                            <button onclick="editUser({{ $user->id }})" class="btn btn-primary d-flex align-items-center justify-content-center" style="width: 26px; height: 26px; padding: 0;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-pencil-square" viewBox="0 0 16 16">
                                    <path d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                                    <path fill-rule="evenodd" d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                @endforeach
                @else
                <div class="text-center">
                    <p>Henüz bir partner kullanıcısı bulunmamaktadır.</p>
                </div>
                @endif
            </li>
        </ul>
    </div>
    @endif
    <div class="modal micromodal-slide" id="modal-4" aria-hidden="false" style="position:relative;z-index:3333;">
        <div class="modal__overlay" tabindex="-1" data-micromodal-close>
            <div class="modal__container" role="dialog" aria-modal="true" aria-labelledby="modal-1-title" style="max-width: 600px;">
                <header class="modal__header" style="height: 20px;">
                    <h2 class="modal__title" id="modal-1-title"></h2>
                    <button class="modal__close" aria-label="Close modal" data-micromodal-close></button>
                </header>
                <main class="modal__content" id="modal-4-content">
                    {{-- <livewire:settings.edit-company /> --}}
                    <livewire:settings.create-user />
                </main>
            </div>
        </div>
    </div>

    <div class="modal micromodal-slide" id="modal-5" aria-hidden="false" style="position:relative;z-index:3333;">
        <div class="modal__overlay" tabindex="-1" data-micromodal-close>
            <div class="modal__container" role="dialog" aria-modal="true" aria-labelledby="modal-1-title" style="max-width: 600px;">
                <header class="modal__header" style="height: 20px;">
                    <h2 class="modal__title" id="modal-1-title"></h2>
                    <button class="modal__close" aria-label="Close modal" data-micromodal-close></button>
                </header>
                <main class="modal__content" id="modal-5-content">
                    <livewire:settings.edit-user />
                </main>
            </div>
        </div>
    </div>
</div>