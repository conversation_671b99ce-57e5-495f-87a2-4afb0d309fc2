<div class="contact pt-2 px-0 pb-5">
    <h5 class="text-center mb-0 py-3"><PERSON><PERSON><PERSON><PERSON><PERSON> Düzenle</h5>
    <div class="contact--panel">
        @if($errors->any())
        <div class="alert alert-danger">
            <ul style="margin-bottom: 0">
                @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
        @endif

        @if(session()->has('message'))
        <div class="alert alert-success">
            {{ session('message') }}
        </div>
        @endif

        <div class="contact--body">
            @if($userId)
            <form wire:submit="save" class="mb-0">
                <div class="row">
                    <div class="col-sm-12 col-md-12 col-lg-12">
                        <input wire:model="name" type="text" class="form-control" placeholder="*Ad Soyad:" required>
                    </div>
                    <div class="col-sm-12 col-md-12 col-lg-12">
                        <input wire:model="email" type="email" class="form-control" placeholder="*E-posta:" required>
                    </div>
                    <div class="col-sm-12 col-md-12 col-lg-12 row justify-content-center">
                        <input type="submit" value="Güncelle" class="btn btn--primary">
                    </div>
                </div>
            </form>
            @endif
        </div>
    </div>
</div>