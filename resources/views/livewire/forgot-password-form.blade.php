<form wire:submit.prevent="getMainForm" class="contactForm mb-0">
    @if (Session::has('message'))
        @if($formAlert == 'ok')
            <div class="alert alert-success text-center" role="alert" style="margin-top: 20px;">
                <span>Şifre sıfırlama linkiniz mailinize iletildi. <br> Mail içerisindeki linke tıklayarak sıfırlayabilirsiniz.</span>
            </div>
        @endif
        @if($formAlert== 'fail')
            <div class="alert alert-danger text-center" role="alert" style="margin-top: 20px;">
                <span>Girdiğiniz mail adresine ait kayıt bulunamadı.</span>
            </div>
        @endif
    @endif
    <div class="row">
        <div class="col-sm-12 col-md-12 col-lg-12">
            <input type="email" wire:model="email" class="form-control" placeholder="E-Posta:" required>
            @error('email') <span class="error">{{ $message }} </span> @enderror

        </div>
        <div class="col-sm-6 col-md-12 col-lg-12 row justify-content-center">
            <button type="submit" class="btn btn--primary">Devam Et</button>
        </div>

    </div>
</form>
