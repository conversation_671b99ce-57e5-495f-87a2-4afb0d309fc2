<div role="tabpanel" class="tab-pane active show" id="description">
    <div class="single-product pt-2 px-0 pb-5">
        <ul class="product-review list-unstyled bg-white rounded">
            @forelse ($offerRequests as $offerRequest)
            <li class="review-comment">
                <div class="row justify-content-center">
                    <div class="col-12 col-sm-6 col-md-2 mb-2">
                        <p class="text-black font-11 font-weight-bold">Müşteri Ünvanı</p>
                        <p class="text-black font-15 ">{{ \Illuminate\Support\Str::mask($offerRequest->customer->name, '*', 2) }}</p>
                    </div>
                    <div class="col-6 col-sm-6 col-md-1 mb-2">
                        <p class="text-black font-11 font-weight-bold">Teklif No</p>
                        <p class="text-black font-15 ">{{ $offerRequest->offer_code }}</p>
                    </div>
                    <div class="col-6 col-sm-6 col-md-2 mb-2">
                        <p class="text-black font-11 font-weight-bold">İstenilen Kira Süresi</p>
                        <p class="text-black font-15 ">{{$offerRequest->maturity->name}}</p>
                    </div>
                    <div class="col-6 col-sm-6 col-md-2 mb-2">
                        <p class="text-black font-11 font-weight-bold ">Toplam Ürün Adeti</p>
                        <p class="text-black font-15 ">{{ $offerRequest->offerItems->sum('quantity') }} Adet</p>
                    </div>
                    <div class="col-6 col-sm-6 col-md-2 mb-2">
                        <p class="text-black font-11 font-weight-bold ">Aylık Kira Bedeli (KDV Dahil)</p>
                        <p class="text-black font-15 ">{{number_format($offerRequest->offerItems->sum('renting_total_price_with_taxes'), 2)}} {{ $offerRequest->currency->symbol }}</p>
                    </div>
                    <div class="col-6 col-sm-6 col-md-2 mb-2">
                        <p class="text-black font-11 font-weight-bold">Talep Tarihi</p>
                        <p class="text-black font-15 ">{{$offerRequest->created_at->timezone('Europe/Istanbul')->format('d/m/y H:i')}}</p>
                    </div>
                    <div class="col-6 col-sm-6 col-md-1 d-flex justify-content-center align-items-center px-0 mb-2">
                        <a class="show-icon" href="/kullanici-portali/teklif-talepleri/{{($offerRequest->offer_code)}}">
                            <svg style="width: 24px; height: 24px;" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M15.0007 12C15.0007 13.6569 13.6576 15 12.0007 15C10.3439 15 9.00073 13.6569 9.00073 12C9.00073 10.3431 10.3439 9 12.0007 9C13.6576 9 15.0007 10.3431 15.0007 12Z" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M12.0012 5C7.52354 5 3.73326 7.94288 2.45898 12C3.73324 16.0571 7.52354 19 12.0012 19C16.4788 19 20.2691 16.0571 21.5434 12C20.2691 7.94291 16.4788 5 12.0012 5Z" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                        </a>
                    </div>
                </div>
            </li>
            @empty
            <li class="review-comment">
                <div class="sm-px-10 pt-70 pb-70 d-flex flex-column justify-content-center align-items-center">
                    <h4>Henüz Teklif Talebiniz Yok</h4>
                    <a wire:navigate.hover class="btn btn--primary mt-3" href="{{ route('offer.create') }}">Teklif Talep Et</a>
                </div>
            </li>
            @endforelse
        </ul>
    </div>
</div>