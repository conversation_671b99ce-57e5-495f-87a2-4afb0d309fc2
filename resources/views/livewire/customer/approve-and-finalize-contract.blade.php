<div>
    @if($showModal)
        <div class="modal-backdrop" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.01); z-index: 1040; display: block;"></div>
        <div class="modal fade show" id="contractModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: 1050; display: block; overflow-y: auto;" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document" style="margin: 30px auto; max-width: 800px;">
                <div class="modal-content" style="background-color: white; border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                    <div class="modal-header" style="padding: 1rem; border-bottom: 1px solid #dee2e6; background-color: #f8f9fa;">
                        <h5 class="modal-title" style="margin: 0; color: #333;">Sözleşme Onayı ve Finalize</h5>
                        <button type="button" class="close" wire:click="closeModal" style="background: none; border: none; font-size: 1.5rem; color: #666; cursor: pointer;">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body" style="padding: 1.5rem; color: #333;">
                        @error('access')
                        <div class="alert alert-danger" style="background-color: #f8d7da; color: #721c24; padding: 0.75rem; border-radius: 4px; margin-bottom: 1rem;">
                            {{ $message }}
                        </div>
                        @enderror

                        @if($credibilityRequest)
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <h6 style="color: #495057; margin-bottom: 1rem;">Talep Bilgileri</h6>
                                    <p style="margin-bottom: 0.5rem;"><strong>Ad Soyad:</strong> {{ $credibilityRequest->name }}</p>
                                    <p style="margin-bottom: 0.5rem;"><strong>TCKN:</strong> {{ $credibilityRequest->tckn }}</p>
                                    <p style="margin-bottom: 0.5rem;"><strong>Telefon:</strong> {{ $credibilityRequest->phone }}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6 style="color: #495057; margin-bottom: 1rem;">Tutar Bilgileri</h6>
                                    <p style="margin-bottom: 0.5rem;"><strong>Talep Edilen:</strong> {{ number_format($credibilityRequest->requested_amount, 2, ',', '.') }} TL</p>
                                    <p style="margin-bottom: 0.5rem;"><strong>Onaylanan:</strong> {{ number_format($credibilityRequest->approved_amount, 2, ',', '.') }} TL</p>
                                    @if($credibilityRequest->used_amount)
                                        <p style="margin-bottom: 0.5rem;"><strong>Kullanılan:</strong> {{ number_format($credibilityRequest->used_amount, 2, ',', '.') }} TL</p>
                                    @endif
                                    @if($credibilityRequest->contract_number)
                                        <p style="margin-bottom: 0.5rem;"><strong>Talep No:</strong> {{ $credibilityRequest->contract_number }}</p>
                                    @endif
                                    <p style="margin-bottom: 0.5rem;"><strong>Durum:</strong> {{ $credibilityRequest->status }}</p>
                                </div>
                            </div>

                            <hr style="margin: 1.5rem 0; border-color: #dee2e6;">

                            <div class="form-group" style="margin-bottom: 1.5rem;">
                                <label for="usedAmount" class="form-label" style="display: block; margin-bottom: 0.5rem; color: #495057;">
                                    <strong>Kullanmak İstediğiniz Tutar (TL) {{ $usedAmount }}</strong>
                                </label>
                                <input
                                    type="text"
                                    class="form-control"
                                    id="usedAmount"
                                    data-max-amount="{{ $credibilityRequest->approved_amount }}"
                                    placeholder="0,00"
                                    style="width: 100%; padding: 0.5rem; border: 1px solid #ced4da; border-radius: 4px; font-size: 1rem;"
                                    onkeyup="window.formatCurrency(this)"
                                    oninput="window.formatCurrency(this)"
                                >
                                <!-- Hidden input to store raw amount for Livewire -->
                                <input type="hidden" id="hiddenUsedAmount" wire:model="usedAmount">
                                @error('usedAmount')
                                <div class="invalid-feedback" style="color: #dc3545; font-size: 0.875rem; margin-top: 0.25rem;">
                                    {{ $message }}
                                </div>
                                @enderror
                                <small class="form-text text-muted" style="font-size: 0.875rem; color: #6c757d; margin-top: 0.25rem;">
                                    Minimum: 100,00 TL, Maksimum: {{ number_format($credibilityRequest->approved_amount, 2, ',', '.') }} TL
                                </small>
                            </div>

                            @error('download')
                            <div class="alert alert-danger mt-3" style="background-color: #f8d7da; color: #721c24; padding: 0.75rem; border-radius: 4px; margin-top: 1rem;">
                                {{ $message }}
                            </div>
                            @enderror
                        @endif
                    </div>
                    <div class="modal-footer" style="padding: 1rem; border-top: 1px solid #dee2e6; background-color: #f8f9fa; display: flex; justify-content: flex-end; gap: 0.5rem;">
                        <button type="button" class="btn btn-secondary" wire:click="closeModal" style="padding: 0.5rem 1rem; background-color: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; display: flex; align-items: center; justify-content: center; min-height: 38px;">İptal</button>
                        @if($credibilityRequest)
                            <button type="button"
                                    id="amountButton"
                                    class="btn {{ $contractProcessed ? 'btn-success' : ( ($errors->has('usedAmount') || empty($usedAmount)) ? 'btn-disabled' : 'btn-success') }}"
                                    wire:click="downloadContract"
                                    @if(!$contractProcessed && ($errors->has('usedAmount') || empty($usedAmount))) disabled @endif
                                    @if($isGeneratingContract) wire:poll.1000ms="checkContractReady" @endif
                                    style="padding: 0.5rem 1rem; border: none; border-radius: 4px; cursor: pointer; display: flex; align-items: center; justify-content: center; min-height: 38px; font-weight: 500; transition: all 0.3s ease;">
                                @if($isGeneratingContract)
                                    <span style="display: flex; align-items: center;">
                                        <i class="fa fa-spinner fa-spin mr-2"></i>
                                        Sözleşme Hazırlanıyor...
                                    </span>
                                @elseif($contractProcessed ?? false)
                                    <i class="fa fa-download mr-2"></i>
                                    Sözleşme İndir
                                @else
                                    <span style="display: flex; align-items: center;">
                                        <i class="fa fa-exclamation-circle mr-2" style="opacity: 0.7;"></i>
                                        <span id="buttonText">Tutar Giriniz</span>
                                    </span>
                                @endif
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    @endif

    <script>
        let currentAmount = '';

        window.formatCurrency = function (input, isInitialLoad = false) {
            // Cursor pozisyonunu kaydet
            let cursorPosition = input.selectionStart;
            let value = input.value;
            let originalLength = value.length;

            // Sadece rakam, nokta ve virgül karakterlerini kabul et
            value = value.replace(/[^0-9.,]/g, '');

            // Eğer nokta ile decimal geliyorsa virgüle çevir (sadece ilk yükleme sırasında)
            if (isInitialLoad && value.includes('.') && !value.includes(',')) {
                // Sadece bir tane nokta varsa ve virgül yoksa, noktayı virgüle çevir
                let dotCount = (value.match(/\./g) || []).length;
                if (dotCount === 1) {
                    value = value.replace('.', ',');
                }
            }

            // Birden fazla virgül kontrolü - sadece ilkini tut
            let commaCount = (value.match(/,/g) || []).length;
            if (commaCount > 1) {
                let firstCommaIndex = value.indexOf(',');
                value = value.substring(0, firstCommaIndex + 1) + value.substring(firstCommaIndex + 1).replace(/,/g, '');
            }

            // Virgülle böl
            let parts = value.split(',');
            let integerPart = parts[0] || '';
            let decimalPart = parts[1] || '';

            // Kuruş kısmını max 2 karakter yap
            if (decimalPart.length > 2) {
                decimalPart = decimalPart.substring(0, 2);
            }

            // Tam sayı kısmından noktaları temizle
            let cleanIntegerPart = integerPart.replace(/\./g, '');

            // Binlik ayracı ekle (sadece 3 haneden fazlaysa)
            let formattedInteger = cleanIntegerPart;
            if (cleanIntegerPart.length > 3) {
                formattedInteger = cleanIntegerPart.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
            }

            // Final değeri oluştur
            let formattedValue = formattedInteger;
            if (parts.length === 2 || value.includes(',')) {
                formattedValue += ',' + decimalPart;
            }

            // Input'a değeri set et
            input.value = formattedValue;

            // Cursor pozisyonunu ayarla
            let lengthDifference = formattedValue.length - originalLength;
            let newCursorPosition = cursorPosition + lengthDifference;

            // Cursor pozisyonunu güvenli aralıkta tut
            if (newCursorPosition < 0) newCursorPosition = 0;
            if (newCursorPosition > formattedValue.length) newCursorPosition = formattedValue.length;

            // Cursor pozisyonunu set et
            setTimeout(() => {
                input.setSelectionRange(newCursorPosition, newCursorPosition);
            }, 0);

            // Temiz değeri sakla (Livewire'a göndermek için)
            currentAmount = cleanIntegerPart;
            if (decimalPart) {
                currentAmount += '.' + decimalPart;
            }

            // Hidden input'a değeri yaz (Livewire wire:model için)
            const hiddenInput = document.getElementById('hiddenUsedAmount');
            if (hiddenInput) {
                hiddenInput.value = currentAmount;
                hiddenInput.dispatchEvent(new Event('input', {bubbles: true}));
            }

            // Validation ve buton state güncelleme
            window.validateAndUpdateButton(parseFloat(currentAmount) || 0, formattedValue);
        }

        window.validateAndUpdateButton = function (cleanAmount, formattedValue) {
            const button = document.getElementById('amountButton');
            const buttonText = document.getElementById('buttonText');
            const icon = button.querySelector('.fa-exclamation-circle');
            const input = document.getElementById('usedAmount');
            const maxAmount = parseFloat(input.getAttribute('data-max-amount')) || 0;
            const minAmount = 100;

            // Debug için console'a yazdır
            console.log('maxAmount:', maxAmount, 'cleanAmount:', cleanAmount);

            let isValid = false;
            let errorMessage = '';

            if (!cleanAmount || cleanAmount === 0) {
                errorMessage = 'Tutar Giriniz';
            } else if (cleanAmount < minAmount) {
                errorMessage = 'Minimum 100 TL';
            } else if (maxAmount > 0 && cleanAmount > maxAmount) {
                errorMessage = 'Maksimum ' + new Intl.NumberFormat('tr-TR', {minimumFractionDigits: 2}).format(maxAmount) + ' TL';
            } else {
                isValid = true;
            }

            if (isValid) {
                // Valid state
                button.className = 'btn btn-success';
                button.disabled = false;
                button.style.cssText = 'padding: 0.5rem 1rem; border: none; border-radius: 4px; cursor: pointer; display: flex; align-items: center; justify-content: center; min-height: 38px; font-weight: 500; transition: all 0.3s ease; background-color: #28a745; color: white; box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);';

                if (icon) icon.style.display = 'none';
                buttonText.textContent = formattedValue + ' TL Kullandır';

                // Success alert göster
                window.showSuccessAlert(true);
            } else {
                // Invalid state
                button.className = 'btn btn-disabled';
                button.disabled = true;
                button.style.cssText = 'padding: 0.5rem 1rem; border: none; border-radius: 4px; cursor: not-allowed; display: flex; align-items: center; justify-content: center; min-height: 38px; font-weight: 500; transition: all 0.3s ease; background-color: #f8f9fa; color: #6c757d; border: 1px solid #dee2e6;';

                if (icon) icon.style.display = 'inline';
                buttonText.textContent = errorMessage;

                // Error message göster
                window.showValidationError(cleanAmount > 0 ? errorMessage : '');
                window.showSuccessAlert(false);
            }
        }

        window.showValidationError = function (message) {
            let errorDiv = document.getElementById('validationError');
            if (!errorDiv) {
                errorDiv = document.createElement('div');
                errorDiv.id = 'validationError';
                errorDiv.className = 'invalid-feedback';
                errorDiv.style.cssText = 'color: #dc3545; font-size: 0.875rem; margin-top: 0.25rem; display: block;';
                document.getElementById('usedAmount').parentNode.appendChild(errorDiv);
            }

            errorDiv.textContent = message;
            errorDiv.style.display = message ? 'block' : 'none';

            // Input border color
            const input = document.getElementById('usedAmount');
            if (message) {
                input.className = 'form-control is-invalid';
            } else {
                input.className = 'form-control';
            }
        }

        window.showSuccessAlert = function (show) {
            let successDiv = document.getElementById('successAlert');
            if (!successDiv) {
                successDiv = document.createElement('div');
                successDiv.id = 'successAlert';
                successDiv.className = 'alert alert-success mt-3';
                successDiv.style.cssText = 'background-color: #d4edda; color: #155724; padding: 0.75rem; border-radius: 4px; margin-top: 1rem; display: none;';
                successDiv.innerHTML = '<i class="fa fa-check-circle"></i> Tutar belirlendi! Aşağıdaki butona tıklayarak işlemi tamamlayabilirsiniz.';

                const container = document.getElementById('usedAmount').closest('.form-group').parentNode;
                container.insertBefore(successDiv, container.children[container.children.length - 2]);
            }

            successDiv.style.display = show ? 'block' : 'none';

            // Input border color
            const input = document.getElementById('usedAmount');
            if (show) {
                input.className = 'form-control is-valid';
            }
        }

        window.updateButtonText = function (formattedValue) {
            // Bu fonksiyon artık validateAndUpdateButton tarafından handle ediliyor
        }

        // Livewire event listeners
        document.addEventListener('livewire:init', () => {
            console.log('Livewire initialized, setting up event listeners...');
            
            // Modal açıldığında usedAmount varsa formatla
            const initializeUsedAmount = () => {
                const usedAmountInput = document.getElementById('usedAmount');
                const hiddenUsedAmount = document.getElementById('hiddenUsedAmount');
                
                if (usedAmountInput && hiddenUsedAmount) {
                    // Hidden input'tan mevcut değeri al (Livewire wire:model'den gelir)
                    const currentValue = hiddenUsedAmount.value;
                    console.log('Current usedAmount value:', currentValue);
                    
                    if (currentValue && currentValue !== '' && currentValue !== '0') {
                         // Değeri visible input'a koy ve formatla
                         usedAmountInput.value = currentValue;
                         window.formatCurrency(usedAmountInput, true);
                         console.log('formatCurrency triggered for value:', currentValue);
                     }
                } else {
                    // DOM elementleri henüz hazır değilse 100ms sonra tekrar dene
                    setTimeout(initializeUsedAmount, 100);
                }
            };
            
            // Modal DOM'a eklendikten sonra çalıştır
            setTimeout(initializeUsedAmount, 200);
            
            // Contract generation devam etmesi için listener
            Livewire.on('continueContractGeneration', () => {
                console.log('continueContractGeneration event received');
                // Küçük bir delay ile işlemi devam ettir (spinner görünür olsun diye)
                setTimeout(() => {
                    @this.finishContractGeneration();
                }, 500);
            });

            // Contract download için listener
            Livewire.on('downloadContractFile', (event) => {
                console.log('downloadContractFile event received:', event);
                const data = event; // Event data'sını direkt kullan
                console.log('Download URL:', data.url);
                console.log('Filename:', data.filename);
                
                // Önce link ile dene
                const link = document.createElement('a');
                link.href = data.url;
                link.download = data.filename;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
            });
        });
        
    </script>

    <style>
        /* Disabled buton için özel stil */
        .btn-disabled {
            background-color: #f8f9fa !important;
            color: #6c757d !important;
            border: 1px solid #dee2e6 !important;
            cursor: not-allowed !important;
            position: relative;
        }

        .btn-disabled::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 2px,
                rgba(0, 0, 0, 0.03) 2px,
                rgba(0, 0, 0, 0.03) 4px
            );
            border-radius: 4px;
            pointer-events: none;
        }

        /* Aktif buton hover efekti */
        .btn-success:not(:disabled):hover {
            background-color: #218838 !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4) !important;
        }

        /* Disabled buton hover efektini devre dışı bırak */
        .btn-disabled:hover {
            background-color: #f8f9fa !important;
            transform: none !important;
            box-shadow: none !important;
        }

        /* Transition efektleri */
        .btn {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        /* Input validation styles */
        .form-control.is-valid {
            border-color: #28a745;
            box-shadow: 0 0 0 0.1rem rgba(40, 167, 69, 0.25);
        }

        .form-control.is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.1rem rgba(220, 53, 69, 0.25);
        }

        .btn {
            width: 100%;
            transition: all 0.3s ease;
        }

        .modal {
            background-color: rgb(34 34 34 / 63%);
        }
    </style>
</div>
