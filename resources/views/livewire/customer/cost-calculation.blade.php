<div class="cost-calculation">
    <h6 class="text-center">Projenin Toplam Maliyetini Hesapla</h6>
    <form wire:submit.prevent="calculate" class="mb-4">
        <div class="form-group">
            <label for="projectTotalAmount">Projenin Toplam Maliyeti:</label>
            <input
                x-data
                x-on:input="$el.value = $el.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1').replace(/(\.\d{2})\d+/, '$1')"
                wire:model="projectTotalAmount"
                type="text"
                inputmode="decimal"
                id="projectTotalAmount"
                class="form-control"
                placeholder="Projenin Toplam Maliyetini Girin"
                step="0.01"
                min="0"
                max="999999999.99"
                required>
            @error('projectTotalAmount') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
        <div class="form-group">
            <label for="term">Vade (Ay):</label>
            <select wire:model="term" class="form-control" id="term" required>
                <option value="">Vade Süresini Seçin</option>
                @foreach($terms as $termOption)
                <option value="{{ $termOption->id }}">{{ $termOption->name }}</option>
                @endforeach
            </select>
            @error('term') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
        <button type="submit" class="btn btn-primary">Hesapla</button>
    </form>
    @if($monthlyPayment)
    <div class="result">
        <h6 class="text-center">Aylık Ödeme:</h6>
        <p class="text-center font-weight-bold">{{ number_format($monthlyPayment, 2) }} TL</p>
    </div>
    @endif

</div>