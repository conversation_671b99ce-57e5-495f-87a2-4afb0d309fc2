<section id="blog" class="blog blog-grid pb-90 pt-30" style="background: #dfe3e8; min-height: 100vh;">
    <div class="" style="padding-left: 5%;padding-right: 5%;">
        <div class="row">
            @include('profile.right-menu')
            <div class="col-sm-12 col-md-12 col-lg-12 pl-400 sm-pl-15">
                <div class="d-flex justify-content-between px-50 sm-px-10 py-2 mb-2 align-items-center bg-white rounded">
                    <h5 class="text-center mb-0 py-3">Findeks Sorgusu <PERSON>i</h5>
                    <a wire:navigate.hover class="btn btn-warning" href="{{ route('customer.request_credibility') }}">Talep Oluştur</a>
                </div>

                @include('shared._flash-messages')

                <div class="single-product">
                    <div class="product-tabs">

                        @if(false)
                            <!-- Nav tabs -->
                            <ul class="nav nav-tabs" role="tablist">
                                <li role="presentation"><a class="{{ $selectedTab == 'evaluations' ? 'active show' : '' }}" style="cursor: pointer" role="tab" data-toggle="tab" wire:click="changeSelectedTab('evaluations')">Güncel Teklifler</a></li>
                                <li role="presentation"><a class="{{ $selectedTab == 'completed' ? 'active show' : '' }}" style="cursor: pointer" aria-controls="details" role="tab" data-toggle="tab" wire:click="changeSelectedTab('completed')">Zamanı Dolmuş Teklifler</a></li>
                            </ul>
                        @endif

                        <!-- Tab panes -->
                        <div class="p-4">
                            <div role="tabpanel" class="tab-pane active show" id="description">
                                <div class="single-product pt-2 px-0 pb-5">
                                    <ul class="product-review list-unstyled bg-white rounded">
                                        @forelse ($credibilityRequests as $credibilityRequest)
                                            <li class="credibility-item {{ $credibilityRequest->row_background_color_class }}
                                            @if($credibilityRequest->is_clickable && !$credibilityRequest->can_deliver_products) clickable-row @endif"
                                                @if($credibilityRequest->is_clickable && !$credibilityRequest->can_deliver_products)
                                                    style="cursor: pointer;"
                                                wire:click="openContractModal({{ $credibilityRequest->id }})"
                                                @endif>

                                                <!-- Desktop Layout (1500px ve üzeri) - Grid Format -->
                                                <div class="desktop-grid">
                                                    <div class="grid-item">
                                                        <div class="field-label">Kullanıcı</div>
                                                        <div class="field-value">{{ $credibilityRequest->partnerUser->name }}</div>
                                                    </div>
                                                    <div class="grid-item">
                                                        <div class="field-label">Ad Soyad</div>
                                                        <div class="field-value">{{ $credibilityRequest->name }}</div>
                                                    </div>
                                                    <div class="grid-item">
                                                        <div class="field-label">TCKN</div>
                                                        <div class="field-value">{{ $credibilityRequest->tckn }}</div>
                                                    </div>
                                                    <div class="grid-item">
                                                        <div class="field-label">Telefon</div>
                                                        <div class="field-value">{{ $credibilityRequest->phone }}</div>
                                                    </div>
                                                    <div class="grid-item">
                                                        <div class="field-label">Başvuru Zamanı</div>
                                                        <div class="field-value">{{ $credibilityRequest->created_at->timezone('Europe/Istanbul')->format('d.m.Y H:i') }}</div>
                                                    </div>
                                                    <div class="grid-item text-right">
                                                        <div class="field-label">Talep Edilen</div>
                                                        <div class="field-value">{{ number_format($credibilityRequest->requested_amount ?? 0, 2, ',', '.') }}</div>
                                                    </div>
                                                    <div class="grid-item text-right">
                                                        <div class="field-label">Ön Onay</div>
                                                        <div class="field-value">{{ number_format($credibilityRequest->approved_amount ?? 0, 2, ',', '.') }}</div>
                                                    </div>
                                                    <div class="grid-item text-right">
                                                        <div class="field-label">Kullanılan</div>
                                                        <div class="field-value">{{ number_format($credibilityRequest->used_amount ?? 0, 2, ',', '.') }}</div>
                                                    </div>
                                                    <div class="grid-item text-center">
                                                        <div class="field-label">Talep No</div>
                                                        <div class="field-value">
                                                            @if($credibilityRequest->contract_number)
                                                                <a href="{{ route('download.contract', $credibilityRequest->id) }}"
                                                                   class="download-contract-btn"
                                                                   title="Sözleşmeyi İndir"
                                                                   data-filename="rental-request-{{ $credibilityRequest->contract_number }}.pdf"
                                                                   onclick="event.stopPropagation();"
                                                                >
                                                                    <i class="fa fa-download"></i>
                                                                    {{ $credibilityRequest->contract_number }}
                                                                </a>
                                                            @else
                                                                -
                                                            @endif
                                                        </div>
                                                    </div>
                                                    <div class="grid-item text-center">
                                                        <div class="field-label">Durum</div>
                                                        <div class="field-value">{{ $credibilityRequest->status }}</div>
                                                    </div>
                                                </div>

                                                <!-- Collapsible Layout (1500px altı) -->
                                                <div class="collapsible-item">
                                                    <!-- Collapsible Header - Always Visible -->
                                                    <div class="collapsible-header" onclick="event.preventDefault(); event.stopPropagation(); return toggleCollapsible(this)">
                                                        <div class="header-summary">
                                                            <div class="header-field">
                                                                <span class="header-label">Ad Soyad</span>
                                                                <span class="header-value">{{ $credibilityRequest->name }}</span>
                                                            </div>
                                                            <div class="header-field">
                                                                <span class="header-label">Durum</span>
                                                                <span class="header-status {{ $credibilityRequest->status_raw == 'approved' ? 'approved' : ($credibilityRequest->status_raw == 'rejected' ? 'rejected' : 'pending') }}">
                                                                    {{ $credibilityRequest->status }}
                                                                </span>
                                                            </div>
                                                            <div class="header-field">
                                                                <span class="header-label">Talep Edilen</span>
                                                                <span class="header-amount">{{ number_format($credibilityRequest->requested_amount ?? 0, 2, ',', '.') }} ₺</span>
                                                            </div>
                                                        </div>
                                                        <div class="collapsible-arrow">
                                                            <i class="fa fa-chevron-down"></i>
                                                        </div>
                                                    </div>

                                                    <!-- Collapsible Content - Hidden by Default -->
                                                    <div class="collapsible-content">
                                                        <div class="mobile-list">
                                                            <div class="mobile-row">
                                                                <div class="mobile-field">
                                                                    <span class="mobile-label">Kullanıcı:</span>
                                                                    <span class="mobile-value">{{ $credibilityRequest->partnerUser->name }}</span>
                                                                </div>
                                                                <div class="mobile-field">
                                                                    <span class="mobile-label">TCKN:</span>
                                                                    <span class="mobile-value">{{ $credibilityRequest->tckn }}</span>
                                                                </div>
                                                            </div>

                                                            <div class="mobile-row">
                                                                <div class="mobile-field">
                                                                    <span class="mobile-label">Telefon:</span>
                                                                    <span class="mobile-value">{{ $credibilityRequest->phone }}</span>
                                                                </div>
                                                                <div class="mobile-field">
                                                                    <span class="mobile-label">Başvuru Zamanı:</span>
                                                                    <span class="mobile-value">{{ $credibilityRequest->created_at->timezone('Europe/Istanbul')->format('d.m.Y H:i') }}</span>
                                                                </div>
                                                            </div>

                                                            <div class="mobile-row triple">
                                                                <div class="mobile-field">
                                                                    <span class="mobile-label">Ön Onay:</span>
                                                                    <span class="mobile-value">{{ number_format($credibilityRequest->approved_amount ?? 0, 2, ',', '.') }}</span>
                                                                </div>
                                                                <div class="mobile-field">
                                                                    <span class="mobile-label">Kullanılan:</span>
                                                                    <span class="mobile-value">{{ number_format($credibilityRequest->used_amount ?? 0, 2, ',', '.') }}</span>
                                                                </div>
                                                                <div class="mobile-field">
                                                                    <span class="mobile-label">Talep No:</span>
                                                                    @if($credibilityRequest->contract_number)
                                                                        <a href="{{ route('download.contract', $credibilityRequest->id) }}"
                                                                           class="download-contract-btn mobile-download"
                                                                           title="Sözleşmeyi İndir"
                                                                           data-filename="rental-request-{{ $credibilityRequest->contract_number }}.pdf"
                                                                           onclick="event.stopPropagation();"
                                                                        >
                                                                            <i class="fa fa-download"></i>
                                                                            {{ $credibilityRequest->contract_number }}
                                                                        </a>
                                                                    @else
                                                                        <span class="mobile-value">-</span>
                                                                    @endif
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Status Messages - Responsive -->
                                                @if(in_array($credibilityRequest->status_raw, ['approved', 'partial_approved', 'payment_pending']))
                                                    <div class="status-message" onclick="event.stopPropagation();">
                                                        <div class="alert {{ $credibilityRequest->can_deliver_products ? 'alert-success' : 'alert-warning' }}">
                                                            <i class="fa {{ $credibilityRequest->can_deliver_products ? 'fa-check-circle' : 'fa-exclamation-triangle' }}"></i>
                                                            <span class="{{ $credibilityRequest->delivery_status_class }}">{{ $credibilityRequest->delivery_status_message }}</span>
                                                        </div>
                                                    </div>
                                                @endif

                                                @if($credibilityRequest->is_clickable && !$credibilityRequest->can_deliver_products)
                                                    <div class="status-message" onclick="event.stopPropagation();">
                                                        <div class="clickable-hint">
                                                            <i class="fa fa-hand-pointer-o"></i>
                                                            Sözleşme işlemleri için tıklayınız
                                                        </div>
                                                        <!-- Mobil için özel buton -->
                                                        <div class="mobile-contract-button"
                                                             @if($credibilityRequest->is_clickable && !$credibilityRequest->can_deliver_products)
                                                                 wire:click="openContractModal({{ $credibilityRequest->id }})"
                                                             @endif>
                                                            <i class="fa fa-file-text-o"></i>
                                                            Sözleşme İşlemleri
                                                        </div>
                                                    </div>
                                                @endif

                                                @if($credibilityRequest->sms_error_message)
                                                    <div class="status-message" onclick="event.stopPropagation();">
                                                        <div class="alert alert-danger">
                                                            <i class="fa fa-exclamation-circle"></i>
                                                            {{ $credibilityRequest->sms_error_message }}
                                                            <button type="button" class="close-btn" wire:click="dismissSmsError({{ $credibilityRequest->id }})">
                                                                &times;
                                                            </button>
                                                        </div>
                                                    </div>
                                                @endif
                                            </li>
                                        @empty
                                            <li class="review-comment">
                                                <div class="sm-px-10 pt-70 pb-70 d-flex flex-column justify-content-center align-items-center">
                                                    <h4>Henüz Findeks Sorgusu Talebiniz Yok</h4>
                                                    <a wire:navigate.hover class="btn btn--primary w-50" href="{{ route('customer.request_credibility') }}">
                                                        <i class="fa fa-plus-circle mr-2"></i>
                                                        Findeks Sorgusu Talebi Oluştur
                                                    </a>
                                                </div>
                                            </li>
                                        @endforelse

                                        <!-- Pagination Info and Controls (Footer) -->
                                        @if($credibilityRequests->total() > 0)
                                            <li class="pagination-info-footer">
                                                <div class="d-flex flex-column flex-lg-row align-items-center justify-content-between">
                                                    <div class="pagination-info-text mb-3 mb-lg-0 text-center text-lg-left">
                                                        <div class="pagination-info">
                                                            <span class="d-block d-md-inline">
                                                                Toplam {{ $credibilityRequests->total() }} kayıt bulundu.
                                                            </span>
                                                            <span class="d-block d-md-inline">
                                                                Sayfa {{ $credibilityRequests->currentPage() }} / {{ $credibilityRequests->lastPage() }}
                                                                ({{ $credibilityRequests->firstItem() ?? 0 }}-{{ $credibilityRequests->lastItem() ?? 0 }} arası gösteriliyor)
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="per-page-selector-wrapper">
                                                        <div class="per-page-selector d-flex align-items-center">
                                                            <label for="perPage" class="mb-0 mr-2 text-nowrap">Sayfa başına kayıt:</label>
                                                            <select id="perPage" wire:model.live="perPage" wire:change="resetPage" class="form-control form-control-sm">
                                                                <option value="5">5</option>
                                                                <option value="10">10</option>
                                                                <option value="20">20</option>
                                                                <option value="50">50</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </li>
                                        @endif

                                        <!-- Pagination Links -->
                                        @if($credibilityRequests->hasPages())
                                            <li class="pagination-container">
                                                <div class="pagination-wrapper">
                                                    <div class="d-flex justify-content-center">
                                                        {{ $credibilityRequests->links('vendor.pagination.bootstrap-4') }}
                                                    </div>
                                                </div>
                                            </li>
                                        @endif
                                    </ul>
                                </div>
                            </div>

                        </div>
                        <!-- #tab-content end -->
                    </div>
                </div>
            </div><!-- .col-lg-4 end -->
        </div>
    </div>
    <!-- .container end -->

    <!-- Modal Component -->
    @livewire('customer.approve-and-finalize-contract')

    <style>
        .flash-message {
            animation: slideInFromTop 0.5s ease-out;
            transition: all 0.3s ease;
        }

        .flash-message:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
        }

        .flash-close:hover {
            background-color: rgba(0, 0, 0, 0.1) !important;
            transform: scale(1.1);
        }

        @keyframes slideInFromTop {
            0% {
                opacity: 0;
                transform: translateY(-30px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .clickable-row:hover {
            background-color: rgba(0, 123, 255, 0.1) !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .table-danger {
            background-color: rgba(220, 53, 69, 0.2) !important;
        }

        .table-success {
            background-color: rgba(40, 167, 69, 0.2) !important;
        }

        .table-secondary {
            background-color: rgba(108, 117, 125, 0.2) !important;
        }

        .download-contract-btn {
            background: linear-gradient(45deg, #007bff, #0056b3) !important;
            border: none !important;
            border-radius: 15px !important;
            color: white !important;
            box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200px;
            display: inline-block;
        }

        .download-contract-btn:hover {
            background: linear-gradient(45deg, #0056b3, #004085) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4) !important;
            color: white !important;
            text-decoration: none !important;
        }

        .download-contract-btn i {
            font-size: 10px;
        }

        /* Custom Responsive Layout - Bootstrap'sız */
        .credibility-item {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        /* Desktop Grid Layout (1500px ve üzeri) */
        .desktop-grid {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr 1fr 1.2fr 1fr 1fr 1fr 1fr 1.5fr;
            gap: 12px;
            align-items: start;
            padding: 15px 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .grid-item {
            display: flex;
            flex-direction: column;
        }

        .field-label {
            font-size: 11px;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 4px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .field-value {
            font-size: 14px;
            color: #2d3748;
            line-height: 1.4;
            font-weight: 500;
        }

        /* TCKN ve Telefon için tek satır ve responsive font-size */
        .grid-item:nth-child(3) .field-value, /* TCKN */
        .grid-item:nth-child(4) .field-value { /* Telefon */
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 14px;
            min-width: 0;
            word-break: normal;
        }

        /* Daha küçük ekranlarda font-size küçült */
        @media (max-width: 1599px) {
            .grid-item:nth-child(3) .field-value, /* TCKN */
            .grid-item:nth-child(4) .field-value { /* Telefon */
                font-size: 13px;
            }
        }

        @media (max-width: 1399px) {
            .grid-item:nth-child(3) .field-value, /* TCKN */
            .grid-item:nth-child(4) .field-value { /* Telefon */
                font-size: 12px;
            }
        }

        @media (max-width: 1199px) {
            .grid-item:nth-child(3) .field-value, /* TCKN */
            .grid-item:nth-child(4) .field-value { /* Telefon */
                font-size: 11px;
            }
        }

        /* Mobile List Layout (1500px altı) */
        .mobile-list {
            display: none;
            padding: 15px;
        }

        .mobile-row {
            display: flex;
            margin-bottom: 12px;
            gap: 15px;
        }

        .mobile-row.single {
            flex-direction: column;
        }

        .mobile-row.triple .mobile-field {
            flex: 1;
            min-width: 0;
        }

        .mobile-field {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .mobile-label {
            font-size: 11px;
            font-weight: bold;
            color: #333;
            margin-bottom: 2px;
        }

        .mobile-value {
            font-size: 14px;
            color: #555;
            word-break: break-word;
        }

        /* Download Button Styles */
        .download-contract-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 20px;
            color: white !important;
            padding: 8px 16px;
            text-decoration: none;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .download-contract-btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            color: white !important;
            text-decoration: none !important;
        }

        .download-contract-btn.mobile-download {
            padding: 4px 8px;
            font-size: 10px;
        }

        .download-contract-btn i {
            margin-right: 4px;
            font-size: 10px;
        }

        /* Text Alignment Utilities */
        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }

        /* Responsive Breakpoints */
        @media (max-width: 1499.98px) {
            .desktop-grid {
                display: none !important;
            }
            .mobile-list {
                display: block !important;
            }
        }

        @media (min-width: 1500px) {
            .desktop-grid {
                display: grid !important;
            }
            .mobile-list {
                display: none !important;
            }
        }

        /* Tablet optimizations (768px - 1499px) */
        @media (max-width: 1199px) and (min-width: 768px) {
            .mobile-row.triple {
                flex-wrap: wrap;
            }
            .mobile-row.triple .mobile-field {
                flex-basis: calc(50% - 7.5px);
            }
            .mobile-row.triple .mobile-field:last-child {
                flex-basis: 100%;
            }
        }

        /* Collapsible Tasarım - 1500px altında */
        .collapsible-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 15px;
            background: white;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .collapsible-header {
            padding: 15px 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.3s ease;
        }

        .collapsible-header:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        }

        .collapsible-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            background: white;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .collapsible-content.expanded {
            max-height: 3000px; /* Çok yüksek max-height */
            opacity: 1;
            transform: translateY(0);
        }

        .collapsible-arrow {
            font-size: 16px;
            color: #6c757d;
            transition: transform 0.3s ease;
        }

        .collapsible-arrow.expanded {
            transform: rotate(180deg);
        }

        .header-summary {
            display: flex;
            gap: 20px;
            align-items: center;
            flex: 1;
        }

        .header-field {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .header-label {
            font-size: 10px;
            font-weight: 700;
            color: #495057;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .header-value {
            font-size: 14px;
            font-weight: 600;
            color: #212529;
        }

        .header-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .header-status.approved {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
        }

        .header-status.pending {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
        }

        .header-status.rejected {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
        }

        .header-amount {
            font-size: 16px;
            font-weight: 700;
            color: #007bff;
        }

        /* Masaüstü cihazlarda (1500px üstü) collapsible'i tamamen gizle */
        @media (min-width: 1501px) {
            .collapsible-item {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                height: 0 !important;
                overflow: hidden !important;
            }
        }

        /* Mobil cihazlarda (1500px altı) collapsible göster, masaüstü gizle */
        @media (max-width: 1500px) {
            .collapsible-item {
                display: block !important;
            }
            .mobile-list {
                padding: 20px;
                background: #f8f9fa;
                border-radius: 12px;
                margin: 10px 0;
            }

            .mobile-row {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-bottom: 20px;
                padding: 15px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                border-left: 4px solid #667eea;
            }

            .mobile-row.triple {
                grid-template-columns: 1fr 1fr 1fr;
            }

            .mobile-row.single {
                grid-template-columns: 1fr;
            }

            .mobile-field {
                display: flex;
                flex-direction: column;
                gap: 8px;
                padding: 12px;
                background: #f8f9fa;
                border-radius: 6px;
                border: 1px solid #e9ecef;
            }

            .mobile-label {
                font-size: 11px;
                font-weight: 700;
                color: #495057;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                border-bottom: 2px solid #dee2e6;
                padding-bottom: 4px;
                margin-bottom: 6px;
            }

            .mobile-value {
                font-size: 14px;
                color: #212529;
                font-weight: 600;
                line-height: 1.4;
            }

            .credibility-item {
                margin-bottom: 20px;
                border: 1px solid #dee2e6;
                border-radius: 12px;
                overflow: hidden;
                background: white;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }

            .download-contract-btn.mobile-download {
                padding: 10px 16px;
                font-size: 12px;
                border-radius: 20px;
                font-weight: 600;
                box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
            }

            .status-message {
                margin-top: 20px;
                padding: 15px;
                border-radius: 8px;
                border-left: 4px solid;
            }

            .alert {
                padding: 12px 16px;
                font-size: 14px;
                font-weight: 500;
                border-radius: 8px;
            }
        }

        /* Mobile optimizations (767px ve altı) */
        @media (max-width: 767px) {
            .credibility-item {
                padding: 12px;
                margin-bottom: 8px;
            }

            .mobile-list {
                padding: 12px;
            }

            .mobile-row {
                flex-direction: column;
                gap: 10px;
                margin-bottom: 12px;
            }

            .mobile-row.triple {
                flex-direction: column;
            }

            .mobile-label {
                font-size: 10px;
                font-weight: 600;
                color: #4a5568;
            }

            .mobile-value {
                font-size: 13px;
                color: #2d3748;
                font-weight: 500;
            }

            .download-contract-btn.mobile-download {
                padding: 6px 12px;
                font-size: 10px;
                border-radius: 16px;
            }

            /* Mobile layout padding fix */
            .single-product {
                padding: 10px !important;
            }
        }

        /* Status Messages Styles */
        .status-message {
            width: 100%;
            margin-top: 15px;
            text-align: center;
        }

        .alert {
            padding: 8px 12px;
            border-radius: 4px;
            margin-bottom: 8px;
            position: relative;
            display: inline-block;
            font-size: 13px;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #b8dacc;
            color: #155724;
            border-radius: 8px;
        }

        .alert-warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #f0c674;
            color: #856404;
            border-radius: 8px;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 1px solid #f1aeb5;
            color: #721c24;
            border-radius: 8px;
        }

        .alert i {
            margin-right: 5px;
        }

        .clickable-hint {
            color: #6c757d;
            font-size: 12px;
            font-style: italic;
        }

        .clickable-hint i {
            margin-right: 5px;
        }

        /* Mobil için özel buton */
        .mobile-contract-button {
            display: none;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            text-align: center;
            margin: 15px auto;
            cursor: pointer;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            max-width: 250px;
        }

        .mobile-contract-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .mobile-contract-button i {
            margin-right: 8px;
        }

        /* Mobil cihazlarda buton göster, masaüstünde gizle */
        @media (max-width: 1500px) {
            .mobile-contract-button {
                display: block;
            }
            .clickable-hint {
                display: none;
            }
        }

        @media (min-width: 1501px) {
            .mobile-contract-button {
                display: none;
            }
            .clickable-hint {
                display: block;
            }
        }

        .close-btn {
            border: none;
            background: transparent;
            font-size: 18px;
            line-height: 1;
            color: inherit;
            margin-left: 10px;
            cursor: pointer;
            padding: 0;
        }

        .close-btn:hover {
            opacity: 0.7;
        }

        /* Enhanced Bootstrap 4 Pagination Styles */
        .pagination-nav {
            width: 100%;
        }

        .pagination {
            display: flex;
            padding-left: 0;
            list-style: none;
            border-radius: 0.5rem;
            margin: 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            background: #fff;
        }

        .page-item {
            margin: 0;
        }

        .page-link {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1rem;
            margin: 0;
            line-height: 1.25;
            color: #495057;
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-left: none;
            text-decoration: none;
            transition: all 0.2s ease-in-out;
            font-weight: 500;
            min-width: 44px;
            min-height: 44px;
        }

        .page-item:first-child .page-link {
            border-left: 1px solid #dee2e6;
            border-top-left-radius: 0.5rem;
            border-bottom-left-radius: 0.5rem;
        }

        .page-item:last-child .page-link {
            border-top-right-radius: 0.5rem;
            border-bottom-right-radius: 0.5rem;
        }

        .page-link:hover {
            z-index: 2;
            color: #007bff;
            text-decoration: none;
            background-color: #f8f9fa;
            border-color: #007bff;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.15);
        }

        .page-link:focus {
            z-index: 3;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .page-item.active .page-link {
            z-index: 1;
            color: #fff;
            background-color: #007bff;
            border-color: #007bff;
            box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
            font-weight: 600;
        }

        .page-item.active .page-link:hover {
            background-color: #0056b3;
            border-color: #0056b3;
            transform: none;
        }

        .page-item.disabled .page-link {
            color: #6c757d;
            pointer-events: none;
            cursor: not-allowed;
            background-color: #f8f9fa;
            border-color: #dee2e6;
            opacity: 0.6;
        }

        .page-link i {
            font-size: 0.875rem;
        }

        /* Pagination Info Footer */
        .pagination-info-footer {
            width: 100%;
            background: #f8f9fa;
            border-radius: 0.5rem;
            padding: 1.25rem 1.5rem;
            margin: 1.5rem 0;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .pagination-info-text {
            flex: 1;
            min-width: 0;
        }

        .pagination-info {
            font-size: 0.875rem;
            color: #6c757d;
            margin: 0;
            line-height: 1.6;
        }

        .pagination-info span {
            margin-right: 0.5rem;
        }

        .per-page-selector-wrapper {
            flex-shrink: 0;
        }

        .per-page-selector {
            gap: 0.5rem;
        }

        .per-page-selector label {
            font-size: 0.875rem;
            color: #495057;
            font-weight: 500;
            white-space: nowrap;
        }

        .per-page-selector select {
            width: 70px;
            flex-shrink: 0;
        }

        /* Pagination Container */
        .pagination-container {
            width: 100%;
            margin: 0;
            padding: 0;
        }

        .pagination-wrapper {
            padding: 1.5rem 0;
            margin: 0;
        }

        /* Enhanced pagination styling */
        .pagination-wrapper .pagination {
            margin: 0;
            border-radius: 0.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: inline-flex;
        }

        .pagination-wrapper .page-item .page-link {
            border: none;
            padding: 0.75rem 1rem;
            margin: 0;
            color: #495057;
            background-color: #fff;
            transition: all 0.2s ease-in-out;
        }

        .pagination-wrapper .page-item:not(.active) .page-link:hover {
            background-color: #e9ecef;
            color: #007bff;
            transform: translateY(-1px);
        }

        .pagination-wrapper .page-item.active .page-link {
            background-color: #007bff;
            color: #fff;
            box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
        }

        .pagination-wrapper .page-item.disabled .page-link {
            background-color: #f8f9fa;
            color: #6c757d;
        }

        /* Large screens - maintain horizontal layout */
        @media (min-width: 992px) {
            .pagination-info-footer {
                padding: 1.25rem 1.5rem;
            }

            .pagination-info span:first-child::after {
                content: " ";
                margin: 0 0.5rem;
            }
        }

        /* Tablet Responsive (768px - 991px) */
        @media (max-width: 991px) and (min-width: 768px) {
            .pagination-info-footer {
                padding: 1.125rem 1.25rem;
            }

            .pagination-info {
                font-size: 0.8125rem;
                text-align: center;
            }

            .per-page-selector {
                justify-content: center;
            }

            .per-page-selector select {
                width: 80px;
            }
        }

        /* Mobile Responsive (767px and below) */
        @media (max-width: 767px) {
            .pagination-info-footer {
                padding: 1rem;
                margin: 1rem 0;
            }

            .pagination-info {
                font-size: 0.8125rem;
                text-align: center;
                line-height: 1.5;
            }

            .pagination-info span {
                display: block;
                margin: 0.25rem 0;
            }

            .per-page-selector {
                gap: 0.75rem;
                flex-direction: column;
                align-items: center;
            }

            .per-page-selector label {
                font-size: 0.8125rem;
                text-align: center;
                margin-bottom: 0;
            }

            .per-page-selector select {
                width: 90px;
                text-align: center;
            }

            .pagination-wrapper {
                padding: 1rem 0;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            .pagination {
                flex-wrap: nowrap;
                justify-content: flex-start;
                border-radius: 0.375rem;
                min-width: max-content;
                margin: 0 auto;
            }

            .page-item {
                flex-shrink: 0;
            }

            .page-link {
                padding: 0.5rem 0.75rem;
                font-size: 0.8125rem;
                min-width: 40px;
                min-height: 40px;
            }

            .page-link i {
                font-size: 0.75rem;
            }

            /* Navigation arrows on mobile */
            .page-item:first-child .page-link,
            .page-item:last-child .page-link {
                min-width: 44px;
                padding: 0.5rem 0.5rem;
            }

            /* Center pagination on mobile if it fits */
            .pagination-wrapper {
                display: flex;
                justify-content: center;
            }

            .pagination-wrapper .pagination {
                display: inline-flex;
            }
        }

        /* Extra small mobile (575px and below) */
        @media (max-width: 575px) {
            .pagination-info-footer {
                padding: 0.875rem;
                margin: 0.75rem 0;
            }

            .pagination-info {
                font-size: 0.75rem;
                line-height: 1.4;
            }

            .per-page-selector select {
                width: 70px;
                font-size: 0.8125rem;
            }

            .pagination-wrapper {
                padding: 0.875rem 0;
            }

            .page-link {
                padding: 0.4375rem 0.5625rem;
                font-size: 0.75rem;
                min-width: 36px;
                min-height: 36px;
            }

            .page-link i {
                font-size: 0.6875rem;
            }

            .page-item:first-child .page-link,
            .page-item:last-child .page-link {
                min-width: 40px;
                padding: 0.4375rem 0.4375rem;
            }
        }

        /* Additional utility classes for better alignment */
        .pagination-info-footer > div {
            width: 100%;
            min-height: auto;
        }

        /* Ensure consistent spacing between pagination elements */
        .pagination-container {
            border-top: 1px solid #e9ecef;
            background: #fff;
            border-radius: 0 0 0.5rem 0.5rem;
        }

        /* Remove any conflicting margins */
        .pagination-info-footer + .pagination-container {
            margin-top: 0;
            border-top-left-radius: 0;
            border-top-right-radius: 0;
        }

        /* Fix any potential overflow issues on very small screens */
        @media (max-width: 480px) {
            .pagination-wrapper {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                padding-bottom: 0.5rem;
            }

            .pagination-wrapper::-webkit-scrollbar {
                height: 3px;
            }

            .pagination-wrapper::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 2px;
            }

            .pagination-wrapper::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 2px;
            }

            .pagination-wrapper::-webkit-scrollbar-thumb:hover {
                background: #a8a8a8;
            }
        }

    </style>

    <script>
        // Collapsible toggle function
        function toggleCollapsible(header) {
            event.preventDefault();
            event.stopPropagation();
            event.stopImmediatePropagation();

            const item = header.closest('.collapsible-item');
            const content = item.querySelector('.collapsible-content');
            const arrow = item.querySelector('.collapsible-arrow');

            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                arrow.classList.remove('expanded');
            } else {
                content.classList.add('expanded');
                arrow.classList.add('expanded');
            }

            return false;
        }

        document.addEventListener('DOMContentLoaded', function () {
            document.querySelectorAll('.download-contract-btn').forEach(function (btn) {
                btn.addEventListener('click', function (e) {
                    // Eğer zaten loading durumundaysa tekrar tıklamayı engelle
                    if (this.dataset.loading === 'true') {
                        e.preventDefault();
                        return;
                    }

                    e.preventDefault();

                    this.dataset.loading = 'true';
                    this.dataset.original = this.innerHTML;
                    const fileName = this.getAttribute('data-filename') || 'contract.pdf';
                    this.innerHTML = '<i class="fa fa-spinner fa-spin mr-1"></i> İndiriliyor...';
                    this.style.pointerEvents = 'none';

                    const revert = () => {
                        this.innerHTML = this.dataset.original;
                        this.style.pointerEvents = 'auto';
                        this.dataset.loading = 'false';
                    };

                    fetch(this.href, {
                        headers: { 'X-Requested-With': 'XMLHttpRequest' }
                    })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('İndirme hatası (' + response.status + ')');
                            }
                            return response.blob();
                        })
                        .then(blob => {
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.style.display = 'none';
                            a.href = url;
                            a.download = fileName;
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            revert();
                        })
                        .catch(err => {
                            alert(err.message);
                            revert();
                        });
                });
            });
        });
    </script>
</section>
