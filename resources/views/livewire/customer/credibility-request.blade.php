<div class="contact--body">
    <div class="d-flex justify-content-between w-100">

    </div>

    @if($errors->all())
        <div class="alert alert-danger">
            <ul style="margin-bottom: 0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    @if (session()->has('message'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('message') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Kapat"></button>
        </div>
    @endif

    <form wire:submit="save" class="mb-0">
        <div class="heading heading-2">
            <h4 class="heading--title">Müşteri Bilgileri</h4>
        </div>
        <div class="d-flex flex-wrap mt-1">
            @if(false)
                <div class="col-sm-12 col-md-6 col-lg-6 mb-3">
                    <label for="tax_number"><PERSON><PERSON><PERSON></label>
                    <input wire:model="tax_number" type="text" id="tax_number" class="form-control" required>
                </div>
            @endif
            <div class="col-sm-12 col-md-6 col-lg-6 mb-3">
                <label for="name">Ad Soyad</label>
                <input wire:model="name" type="text" id="name" class="form-control">
            </div>
            <div class="col-sm-12 col-md-6 col-lg-6 mb-3">
                <label for="email">E-posta</label>
                <input wire:model="email" type="email" id="email" class="form-control">
            </div>
            <div class="col-sm-12 col-md-6 col-lg-6 mb-3">
                <label for="tckn">T.C. Kimlik Numarası</label>
                <input wire:model="tckn" type="text" id="tckn" class="form-control" x-mask="99999999999" minlength="11" maxlength="11" required>
            </div>
            <div class="col-sm-12 col-md-6 col-lg-6 mb-3">
                <label for="birth_date">Doğum Tarihi</label>
                <input wire:model="birth_date" type="date" id="birth_date" class="form-control">
            </div>
            <div class="col-sm-12 col-md-6 col-lg-6 mb-3">
                <label for="phone">Findeks Kayıtlı Telefon</label>
                <input wire:model="phone" type="tel" id="phone" class="form-control" required x-mask="(999) 999 99 99">
            </div>
            <div class="col-sm-12 col-md-6 col-lg-6 mb-3">
                <label for="requested_amount">Talep Edilen Tutar</label>
                <input
                    x-data
                    x-on:input="$el.value = $el.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1').replace(/(\.\d{2})\d+/, '$1')"
                    wire:model="requested_amount"
                    type="text"
                    inputmode="decimal"
                    id="requested_amount"
                    class="form-control"
                    step="0.01"
                    min="0"
                    max="999999999.99">
            </div>
            <div class="col-sm-12 col-md-6 col-lg-6 mb-3">
                <label for="maturity">Vade</label>
                <select wire:model="maturity" id="maturity" class="form-control form-select">
                    <option value="">Seçiniz</option>
                    <option value="6">6 Ay</option>
                    <option value="12">12 Ay</option>
                    <option value="24">24 Ay</option>
                </select>
            </div>
            @if(false)
                <div class="col-sm-12 col-md-6 col-lg-6 mb-3">
                    <div class="form-check">
                        <input wire:model="is_firm" type="checkbox" id="is_firm" class="form-check-input">
                        <label for="is_firm" class="form-check-label">Firma mı?</label>
                    </div>
                </div>
            @endif
            <div class="col-sm-12 mb-3">
                <label for="note">Not</label>
                <textarea wire:model="note" id="note" class="form-control" rows="3"></textarea>
            </div>
        </div>

        <div class="col-sm-12 col-md-12 col-lg-12 row justify-content-center mt-4">
            <button type="submit" class="btn btn-primary">
                <i class="fa fa-paper-plane mr-2"></i>
                Talep Gönder
            </button>
        </div>
    </form>
</div>
