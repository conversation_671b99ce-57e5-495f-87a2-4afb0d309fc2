<form wire:submit.prevent="getMainForm" class="contactForm mb-0">
    @if (Session::has('message'))
        @if($formAlert == 'ok')
            <div class="alert alert-success text-center" role="alert" style="margin-top: 20px;">
                <span>Şifre sıfırlama işleminiz tamamlandı.</span>
            </div>
        @endif
        @if($formAlert== 'fail')
            <div class="alert alert-danger text-center" role="alert" style="margin-top: 20px;">
                <span>Şifre sıfırlama işleminiz tamamlanmadı.</span>
            </div>
        @endif
    @endif
    <div class="row">
        <div class="col-sm-12 col-md-12 col-lg-12">
            <input type="password" wire:model="password" class="form-control" placeholder="Şifre" required>
            @error('password') <span class="error">{{ $message }} </span> @enderror
        </div>
        <div class="col-sm-12 col-md-12 col-lg-12">
            <input type="password" wire:model="password_confirmation" class="form-control" placeholder="<PERSON><PERSON>re Tekrar" required>
            @error('password_confirmation') <span class="error">{{ $message }} </span> @enderror
        </div>
        <input wire:model="token" type="hidden" />
        <div class="col-sm-6 col-md-12 col-lg-12 row justify-content-center">
            <button type="submit" class="btn btn--primary">Devam Et</button>
        </div>

    </div>
</form>
