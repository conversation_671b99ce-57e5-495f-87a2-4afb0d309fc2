<div class="w-100 d-flex flex-wrap review-comment p-3 mb-3 position-relative">
    @if(!$is_edit)
        <div class="row w-100">
            <div class="col-md-3">
                <p class="text-black font-11 font-weight-bold"><PERSON><PERSON><PERSON><PERSON></p>
                <p class="text-black font-15 ">{{$item->productName}}</p>
            </div>
            <div class="col-md-2">
                <p class="text-black font-11 font-weight-bold">Ana Kategori </p>
                <p class="text-black font-15 ">{{$item->selectedTopCategory == -1 ? 'Diğer' : \App\Models\Category::find($item->selectedTopCategory)->name}}</p>
            </div>
            {{--            <div class="col-md-2">--}}
            {{--                <p class="text-black font-11 font-weight-bold">Alt Kategori</p>--}}
            {{--                <p class="text-black font-15 ">{{$item->selectedTopCategory === -1 ? $item->specialCategory : \App\Models\Category::find($item->selectedSubCategory)?->name ?? 'Diğer' }}</p>--}}
            {{--            </div>--}}
            <div class="col-md-2">
                <p class="text-black font-11 font-weight-bold text-right">Birim Fiyat KDV Dahil</p>
                <p class="text-black font-15 text-right"><span>{{$item->productPrice}}</span> {{ \App\Models\Currency::find($item->selectedCurrency)?->symbol ?? '₺' }}</p>
            </div>
            <div class="col-md-1">
                <p class="text-black font-11 font-weight-bold text-right">Adet</p>
                <p class="text-black font-15 text-right">{{$item->amount}}</p>
            </div>

            <div class="col-md-2 d-flex justify-content-end">
                <svg wire:click="edit" style="width: 20px; fill: #0c273a" version="1.1" id="Capa_1"
                     xmlns="http://www.w3.org/2000/svg"
                     viewBox="0 0 348.882 348.882" xml:space="preserve">
                <g>
                    <path d="M333.988,11.758l-0.42-0.383C325.538,4.04,315.129,0,304.258,0c-12.187,0-23.888,5.159-32.104,14.153L116.803,184.231
		c-1.416,1.55-2.49,3.379-3.154,5.37l-18.267,54.762c-2.112,6.331-1.052,13.333,2.835,18.729c3.918,5.438,10.23,8.685,16.886,8.685
		c0,0,0.001,0,0.001,0c2.879,0,5.693-0.592,8.362-1.76l52.89-23.138c1.923-0.841,3.648-2.076,5.063-3.626L336.771,73.176
		C352.937,55.479,351.69,27.929,333.988,11.758z M130.381,234.247l10.719-32.134l0.904-0.99l20.316,18.556l-0.904,0.99
		L130.381,234.247z M314.621,52.943L182.553,197.53l-20.316-18.556L294.305,34.386c2.583-2.828,6.118-4.386,9.954-4.386
		c3.365,0,6.588,1.252,9.082,3.53l0.419,0.383C319.244,38.922,319.63,47.459,314.621,52.943z"/>
                    <path d="M303.85,138.388c-8.284,0-15,6.716-15,15v127.347c0,21.034-17.113,38.147-38.147,38.147H68.904
		c-21.035,0-38.147-17.113-38.147-38.147V100.413c0-21.034,17.113-38.147,38.147-38.147h131.587c8.284,0,15-6.716,15-15
		s-6.716-15-15-15H68.904c-37.577,0-68.147,30.571-68.147,68.147v180.321c0,37.576,30.571,68.147,68.147,68.147h181.798
		c37.576,0,68.147-30.571,68.147-68.147V153.388C318.85,145.104,312.134,138.388,303.85,138.388z"/>
                </g>
            </svg>
                <template x-if="$wire.$parent.form.offerItems.length > 1">
                    <svg wire:click="trash" id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg"
                         viewBox="0 0 110.61 122.88" style="width: 20px; fill: #FF2D20" class="ml-2">
                        <path
                            d="M39.27,58.64a4.74,4.74,0,1,1,9.47,0V93.72a4.74,4.74,0,1,1-9.47,0V58.64Zm63.6-19.86L98,103a22.29,22.29,0,0,1-6.33,14.1,19.41,19.41,0,0,1-13.88,5.78h-45a19.4,19.4,0,0,1-13.86-5.78l0,0A22.31,22.31,0,0,1,12.59,103L7.74,38.78H0V25c0-3.32,1.63-4.58,4.84-4.58H27.58V10.79A10.82,10.82,0,0,1,38.37,0H72.24A10.82,10.82,0,0,1,83,10.79v9.62h23.35a6.19,6.19,0,0,1,1,.06A3.86,3.86,0,0,1,110.59,24c0,.2,0,.38,0,.57V38.78Zm-9.5.17H17.24L22,102.3a12.82,12.82,0,0,0,3.57,8.1l0,0a10,10,0,0,0,7.19,3h45a10.06,10.06,0,0,0,7.19-3,12.8,12.8,0,0,0,3.59-8.1L93.37,39ZM71,20.41V12.05H39.64v8.36ZM61.87,58.64a4.74,4.74,0,1,1,9.47,0V93.72a4.74,4.74,0,1,1-9.47,0V58.64Z"/>
                    </svg>
                </template>
            </div>
        </div>
    @endif
    @if($is_edit)
        <div class="col-md-12">
            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <li>Hatalı alanları lütfen düzenleyiniz.</li>
                    </ul>
                    {{--                        @foreach ($errors->all() as $error)--}}
                    {{--                            <li>{{ $error }}</li>--}}
                    {{--                        @endforeach--}}
                </div>
            @endif
        </div>
        <div class="col-sm-6 col-md-6 col-lg-6">
            <label>Ana Kategori Adı *</label>
            <select name="mal-alim-satim" class="form-control form-select @error('item.selectedTopCategory'){{'error'}}@enderror" aria-label="mal-alim-satim" wire:model.live="item.selectedTopCategory" required aria-required="true">
                <option value="">Seçiniz</option>
                @foreach($topCategories as $category)
                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                @endforeach
                <option value="-1">Diğer</option>
            </select>
            @error('item.selectedTopCategory')
            <div class="error-message">
                {{ $message }}
            </div>
            @enderror
        </div>
        @if ($item->selectedTopCategory === -1)
            <div class="col-sm-6 col-md-6 col-lg-6">
                <label>Diğer Kategori Adı</label>
                <input type="text" wire:model="item.specialCategory" class="form-control @error('item.specialCategory'){{'error'}}@enderror" required="" aria-required="true"/>
                @error('item.specialCategory')
                <div class="error-message">
                    {{ $message }}
                </div>
                @enderror
            </div>
        @endif
        @if (false && $item->selectedTopCategory != -1)
            <div class="col-sm-6 col-md-6 col-lg-6">
                <label>Alt Kategori Adı *</label>
                <select name="mal-alim-satim" class="form-control form-select @error('item.selectedSubCategory'){{'error'}}@enderror" aria-label="mal-alim-satim"
                        wire:model.live="item.selectedSubCategory"
                        {{ $this->item->selectedTopCategory ? '' : 'disabled' }} required>
                    <option selected=""></option>
                    @if($this->item->subCategories)
                        @foreach($this->item->subCategories as $category)
                            <option value="{{ $category->id }}">{{ $category->name }}</option>
                        @endforeach
                    @endif
                    <option value="-1">Diğer</option>
                </select>
                @error('item.selectedSubCategory')
                <div class="error-message">
                    {{ $message }}
                </div>
                @enderror
            </div>
        @endif
        <div class="col-sm-12 col-md-12 col-lg-12 ">
            <label>Ürün Adı *</label>
            <input wire:model.blur="item.productName" type="text" name="product_name" class="form-control @error('item.productName'){{'error'}}@enderror" required="" aria-required="true">
            @error('item.productName')
            <div class="error-message">
                {{ $message }}
            </div>
            @enderror
        </div>
        <div class="col-sm-12 col-md-12 col-lg-12 ">
            <label>Ürün Linki</label>
            <input wire:model.blur="item.productLink" type="url" name="product_link" class="form-control @error('item.productLink'){{'error'}}@enderror">
            @error('item.productLink')
            <div class="error-message">
                {{ $message }}
            </div>
            @enderror
        </div>
        <div class="col-sm-12 col-md-12 col-lg-12 ">
            <label>Ürün Açıklaması</label>
            <textarea wire:model.blur="item.productDescription" rows="2" cols="10" name="product_description" class="form-control @error('item.productDescription'){{'error'}}@enderror" aria-required="true"></textarea>
            @error('item.productDescription')
            <div class="error-message">
                {{ $message }}
            </div>
            @enderror
        </div>
        <div class="col-sm-4 col-md-4 col-lg-4">
            <label>Birim Fiyat KDV Hariç *</label>
            <input wire:model.blur="item.productPrice" type="number" name="product_price" class="form-control per-price @error('item.productPrice'){{'error'}}@enderror" required="" aria-required="true" min="1">
            @error('item.productPrice')
            <div class="error-message">
                {{ $message }}
            </div>
            @enderror
        </div>
        <div class="col-sm-4 col-md-4 col-lg-4">
            <label>Para Birimi</label>
            <select name="mal-alim-satim" class="form-control form-select @error('item.selectedCurrency'){{'error'}}@enderror" aria-label="mal-alim-satim" wire:model.live="item.selectedCurrency" required>
                @if($currencies)
                    @foreach($currencies as $currency)
                        <option value="{{ $currency->id }}">{{ $currency->name }}</option>
                    @endforeach
                @endif
            </select>
            @error('item.selectedCurrency')
            <div class="error-message">
                {{ $message }}
            </div>
            @enderror
        </div>
        <div class="col-sm-4 col-md-4 col-lg-4">
            <label>Adet *</label>
            <input wire:model.live.debounce="item.amount" type="number" name="product_price" class="form-control amount @error('item.amount'){{'error'}}@enderror" required="" aria-required="true" min="1">
            @error('item.amount')
            <div class="error-message">
                {{ $message }}
            </div>
            @enderror
        </div>
        <div class="col-sm-12 col-md-12 col-lg-12 ">
            <div class="file-center">
                <div class="dropzone">
                    <div class="title">
                        <h6>Proformayı buradan yükleyebilirsiniz.</h6>
                    </div>
                    <img src="/doc/upload.svg" class="upload-icon">
                    <input type="file" wire:model.blur="item.document" class="upload-input @error('item.document'){{'error'}}@enderror">
                </div>
                @error('item.document')
                    <div class="error-message">
                        {{ $message }}
                    </div>
                @enderror
            </div>
        </div>
        <div class="col-sm-12 col-md-12 col-lg-12 d-flex justify-content-center">
            <input type="button" value="Yeni Ürün Ekle" class="btn btn--primary w-auto px-3 mb-2" wire:click="edit">
            <template x-if="$wire.$parent.form.offerItems.length > 1">
                <input type="button" value="İptal" class="btn btn--primary w-auto px-3 mb-2 ml-30" wire:click="cancel">
            </template>
        </div>
    @endif
</div>
