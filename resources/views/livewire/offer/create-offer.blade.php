<div class="contact--body">
    <div class="d-flex justify-content-between w-100">
        {{--        <div class="btn btn--bordered btn-warning btn-sm float-right set-back-button" style="" wire:click="setBackButton">--}}
        {{--            <svg class="svg-icon" style="width: 20px;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">--}}
        {{--                <path d="M475 276V141.4c-12.1-56.3-58.2-22-58.2-22L96.6 395.9c-70.4 48.9-4.8 85.7-4.8 85.7l315.4 274.1c63.1 46.5 67.9-24.5 67.9-24.5V606.4C795.3 506 926.3 907.5 926.3 907.5c12.1 22 19.4 0 19.4 0C1069.4 305.4 475 276 475 276z"/>--}}
        {{--            </svg>--}}
        {{--        </div>--}}
    </div>

    @if($errors->all())
        <div class="alert alert-danger">
            <ul style="margin-bottom: 0">
                @foreach($errors->all() as  $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form wire:submit="save" class="mb-0">
        <div class="heading heading-2">
            <h4 class="heading--title">Müşteri</h4>
        </div>
        <div class="d-flex flex-wrap mt-1">
            <div class="col-sm-12 col-md-6 col-lg-6">
                <label>Müşteri Vergi Numarası</label>
                <input wire:model.live.debounce.250ms="form.customerTaxNumber" type="text" name="vergi-numarasi" class="form-control" required="" aria-required="true">
            </div>
            <div class="col-sm-12 col-md-6 col-lg-6">
                <label>Müşteri Vergi dairesi</label>
                <input wire:model="form.customerTaxOffice" type="text" name="IBAN" class="form-control" required="" aria-required="true" {{ $form->disableCustomerFields ? 'disabled' :  ''}}>
            </div>
            <div class="col-sm-12 col-md-6 col-lg-6">
                <label>Müşteri Telefon</label>
                <input wire:model="form.customerPhone" type="text" name="phone" class="form-control" required="" aria-required="true" {{ $form->disableCustomerFields ? 'disabled' :  ''}}>
            </div>
            <div class="col-sm-12 col-md-6 col-lg-6">
                <label>Müşteri Email</label>
                <input wire:model="form.customerEmail" type="text" name="email" class="form-control" required="" aria-required="true" {{ $form->disableCustomerFields ? 'disabled' :  ''}}>
            </div>
            <div class="col-sm-12 col-md-12 col-lg-12">
                <label>Müşteri Şirket Ünvanı</label>
                <input wire:model="form.customerFirmName" type="text" name="sirket-unvani" class="form-control" required="" aria-required="true" {{ $form->disableCustomerFields ? 'disabled' :  ''}}>
            </div>
            <div class="col-sm-12 col-md-8 col-lg-6">
                <label>Teklif Kira Süresi</label>
                <select name="istenilen-vade" class="form-control form-select" aria-label="istenilen-vade" wire:model="form.selectedMaturityTerm" required>
                    <option selected=""></option>
                    @foreach($form->maturityTerms as $maturityTerm)
                        <option value="{{ $maturityTerm->id }}">{{ $maturityTerm->name }}</option>
                    @endforeach
                </select>
            </div>
            <div class="col-sm-12 col-md-4 col-lg-6">
                <label>Teklif Para Birimi</label>
                <select name="mal-alim-satim" class="form-control form-select" aria-label="mal-alim-satim" wire:model.live="form.selectedCurrency" required>
                    <option selected=""></option>
                    @if($form->currencies)
                        <option value="1">Türk Lirası</option>
                        {{--                        @foreach($form->currencies as $currency)--}}
                        {{--                        @endforeach--}}
                    @endif
                </select>
            </div>
        </div>
        <div class="heading heading-2 row">
            <div class="col-sm-12 col-md-6 col-lg-6">
                <h4 class="heading--title">Ürünler ({{ $form->offerItems->count() }})</h4>
            </div>
            <div class="col-sm-12 col-md-6 col-lg-6 d-flex justify-content-end">
                <input type="button" value=" Ürün Ekle" class="btn btn--primary w-auto px-3 mb-2" wire:click="addOfferItem">
            </div>
        </div>
        <div class="">
            @foreach($form->offerItems as $key => $item)
                <livewire:offer.item :$item :key="$key" :iter="$key" :topCategories="$form->topCategories"/>
            @endforeach
        </div>

        {{--        <div class="col-sm-6 col-md-6 col-lg-6">--}}
        {{--            <input wire:model="form.totalPrice" type="number" name="toplam-tutar" class="form-control" placeholder="*Toplam satın alma tutarı :" required="" aria-required="true">--}}
        {{--        </div>--}}
        {{--        <div class="col-sm-12 col-md-12 col-lg-12 ">--}}
        {{--            <div class="file-center">--}}
        {{--                <div class="dropzone">--}}
        {{--                    <div class="title">--}}
        {{--                        <h6>Ek Dökümanı buradan yükleyebilirsiniz.</h6>--}}
        {{--                    </div>--}}
        {{--                    <img src="/doc/upload.svg" class="upload-icon">--}}
        {{--                    <input type="file" class="upload-input" wire:model="form.additionalPurchaseDocument">--}}
        {{--                </div>--}}
        {{--            </div>--}}
        {{--        </div>--}}
        <div class="col-sm-12 col-md-12 col-lg-12 row justify-content-center mt-4">
            <input type="submit" value="Teklif Al" class="btn btn-warning">
        </div>
    </form>
</div>
