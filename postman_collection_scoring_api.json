{"info": {"name": "Scoring API Collection", "description": "SendCustomerScoringRequest job'ında kull<PERSON> /api/scoring endpoint'i <PERSON><PERSON><PERSON> collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Send Customer Scoring Request", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{SCORING_API_KEY}}", "description": "API anahtarı - Environment variable olarak tanımlanmalı"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"scoring_source_id\": 1,\n    \"full_name\": \"<PERSON><PERSON> Yılmaz\",\n    \"tckn\": \"12345678901\",\n    \"email\": \"<EMAIL>\",\n    \"birth_date\": \"1985-03-15\",\n    \"requested_amount\": 50000.00,\n    \"requested_duration_months\": 12,\n    \"additional_data\": {\n        \"phone\": \"+905551234567\",\n        \"source_data_id\": 123,\n        \"renting_ratio\": 1.5,\n        \"partner_data\": {\n            \"name\": \"ABC Teknoloji Ltd. Şti.\",\n            \"phone\": \"+905551234568\"\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{SCORING_API_BASE_URL}}/api/scoring", "host": ["{{SCORING_API_BASE_URL}}"], "path": ["api", "scoring"]}, "description": "Müşteri kredibilite talebi gönderir. Bu endpoint SendCustomerScoringRequest job'ı tarafından kullanılır."}, "response": [{"name": "Başarılı Response Örneği", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer your-api-key-here"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"scoring_source_id\": 1,\n    \"full_name\": \"<PERSON><PERSON> Yılmaz\",\n    \"tckn\": \"12345678901\",\n    \"email\": \"<EMAIL>\",\n    \"birth_date\": \"1985-03-15\",\n    \"requested_amount\": 50000.00,\n    \"requested_duration_months\": 12,\n    \"additional_data\": {\n        \"phone\": \"+905551234567\",\n        \"source_data_id\": 123,\n        \"renting_ratio\": 1.5,\n        \"partner_data\": {\n            \"name\": \"ABC Teknoloji Ltd. Şti.\",\n            \"phone\": \"+905551234568\"\n        }\n    }\n}"}, "url": {"raw": "https://api.example.com/api/scoring", "protocol": "https", "host": ["api", "example", "com"], "path": ["api", "scoring"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Scoring request received successfully\",\n    \"data\": {\n        \"ulid\": \"01HXAMPLE123456789ABCDEF\",\n        \"status\": \"processing\",\n        \"request_id\": \"req_123456789\"\n    }\n}"}, {"name": "Hata Response Örneği", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer invalid-api-key"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"scoring_source_id\": 1,\n    \"full_name\": \"<PERSON><PERSON> Yılmaz\"\n}"}, "url": {"raw": "https://api.example.com/api/scoring", "protocol": "https", "host": ["api", "example", "com"], "path": ["api", "scoring"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": false,\n    \"message\": \"Validation failed\",\n    \"errors\": {\n        \"tckn\": [\"TCKN field is required\"],\n        \"email\": [\"Email field is required\"],\n        \"birth_date\": [\"Birth date field is required\"]\n    }\n}"}]}], "variable": [{"key": "SCORING_API_BASE_URL", "value": "https://api.example.com", "description": "Scoring API'nin base URL'i"}, {"key": "SCORING_API_KEY", "value": "your-api-key-here", "description": "Scoring API için Bearer token"}]}