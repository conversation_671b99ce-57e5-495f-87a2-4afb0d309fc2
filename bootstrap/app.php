<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use App\Http\Middleware\ImpersonateMiddleware;
use App\Http\Middleware\DebugPlatformEnabled;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Özel middleware'i burada tanımlayalım
        $middleware->alias([
            'impersonate' => ImpersonateMiddleware::class,
            'debug-platform' => DebugPlatformEnabled::class,
        ]);

        // Debug API route'larını CSRF korumasından muaf tut
        $middleware->validateCsrfTokens(except: [
            'debug/*',
        ]);
    })
    ->withProviders([
        // App\Providers\AuthServiceProvider::class,
    ])
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
