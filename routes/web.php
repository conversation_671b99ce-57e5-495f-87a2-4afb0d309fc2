<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\UserPortalController;
use App\Http\Controllers\Debug\DebugController;
use App\Http\Controllers\Debug\ApiTestController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

Route::get('/', [\App\Http\Controllers\AuthController::class, 'login'])->name('login');
Route::get('sifremi-unuttum', [\App\Http\Controllers\AuthController::class, 'forgotPassword'])->name('auth.forgot-password');
Route::get('cikis-yap', [\App\Http\Controllers\AuthController::class, 'loggedOut'])->name('logged-out');

// Dashboard routes
Route::prefix('kullanici-portali')->middleware('auth')->group(function () {
    Route::get('/', [UserPortalController::class, 'index'])->name('dashboard');
    Route::get('teklif-talepleri', [UserPortalController::class, 'offerRequests'])->name('offer.index');
    Route::get('teklif-talepleri/{projectCode}', [UserPortalController::class, 'showOfferDetail'])->name('offer.detail');
    Route::get('talep-ac', [UserPortalController::class, 'openRequest'])->name('offer.create');
    Route::get('belgeler', [UserPortalController::class, 'documents'])->name('documents');
    Route::get('ayarlar', [UserPortalController::class, 'settings'])->name('settings');
    Route::get('indirme-teklifi/{projectCode}', [UserPortalController::class, 'downloadOffer'])->name('download-offer');
    Route::get('cari-kredibilite-talebi', [UserPortalController::class, 'requestCredibility'])->name('customer.request_credibility');
    Route::get('cari-kredibilite-talepleri', [UserPortalController::class, 'credibilityRequestList'])->name('customer.credibility_request_list');
    Route::get('sozlesme-indir/{credibilityRequest}', [UserPortalController::class, 'downloadContract'])->name('download.contract');
    Route::get('maliyet-hesaplama', [UserPortalController::class, 'costCalculate'])->name('cost.calculate');

    // Impersonation route'u - sadece <EMAIL> için
    Route::get('kullanici-giris/{email}', [UserPortalController::class, 'impersonate'])
        ->name('impersonate')
        ->middleware('impersonate');
});

// Sözleşme oluşturma
Route::get('sozlesme-olustur/{offer}', [\App\Http\Controllers\B2BAgrementController::class, 'create'])->name('b2b-agreement');

//Route::get('b2b-sozlesme', [\App\Http\Controllers\B2BAgrementController::class, 'index'])->name('b2b-agreement');

Route::get('/reset-password/{token}', function (Request $request, $token) {
    $reset = DB::table('password_reset_tokens')->where('token', $token)->first();

    if (!$reset) {
        return redirect()->route('home');
    }

    return view('reset-password', ['token' => $token]);
})->name('resetPasswordForm');

// Debug Platform Routes
Route::prefix('debug')
    ->middleware(['web', 'debug-platform'])
    ->name('debug.')
    ->group(function () {
        // Debug dashboard
        Route::get('/', [DebugController::class, 'dashboard'])->name('dashboard');

        // API Testing
        Route::get('/api-tester', [DebugController::class, 'apiTester'])->name('api-tester');
        Route::post('/api-test', [ApiTestController::class, 'executeTest'])->name('api-test');
        Route::get('/templates/{template}', [DebugController::class, 'getTemplate'])->name('template');
    });
