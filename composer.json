{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "doctrine/dbal": "^4.2", "filament/filament": "^3.2", "google/apiclient": "^2.15.0", "laravel/framework": "^11.9", "laravel/tinker": "^2.9", "opcodesio/log-viewer": "^3.12", "rmsramos/activitylog": "^1.0", "solution-forest/filament-tree": "^2.0", "spatie/laravel-ignition": "^2.7", "spatie/laravel-model-states": "^2.7", "spatie/laravel-pdf": "^1.5"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.13", "fakerphp/faker": "^1.23", "laradumps/laradumps": "^3.0", "laravel/boost": "^1.0", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0.1"}, "autoload": {"files": ["app/helper.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}